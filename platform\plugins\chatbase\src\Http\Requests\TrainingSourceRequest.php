<?php

namespace Botble\Chatbase\Http\Requests;

use Botble\Support\Http\Requests\Request;

class TrainingSourceRequest extends Request
{
    public function rules(): array
    {
        return [
            'type' => ['required', 'string', 'in:text,url,file'],
            'title' => ['required', 'string', 'max:255'],
            'content' => ['required_if:type,text', 'nullable', 'string', 'max:50000'],
            'url' => ['required_if:type,url', 'nullable', 'url', 'max:500'],
            'file' => ['required_if:type,file', 'nullable', 'file', 'mimes:txt,pdf,doc,docx', 'max:10240'], // 10MB max
        ];
    }

    public function attributes(): array
    {
        return [
            'type' => trans('plugins/chatbase::chatbase.training.source_type'),
            'title' => trans('plugins/chatbase::chatbase.training.source_title'),
            'content' => trans('plugins/chatbase::chatbase.training.source_content'),
            'url' => trans('plugins/chatbase::chatbase.training.source_url'),
            'file' => trans('plugins/chatbase::chatbase.training.source_file'),
        ];
    }

    public function messages(): array
    {
        return [
            'content.required_if' => 'Content is required when type is text.',
            'url.required_if' => 'URL is required when type is URL.',
            'file.required_if' => 'File is required when type is file.',
        ];
    }
}
