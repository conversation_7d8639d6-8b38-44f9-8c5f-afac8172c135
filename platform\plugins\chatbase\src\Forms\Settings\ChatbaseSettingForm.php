<?php

namespace Bo<PERSON>ble\Chatbase\Forms\Settings;

use Botble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\Fields\OnOffCheckboxField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Chatbase\Http\Requests\Settings\ChatbaseSettingRequest;
use Bo<PERSON><PERSON>\Setting\Forms\SettingForm;

class ChatbaseSettingForm extends SettingForm
{
    public function setup(): void
    {
        parent::setup();

        $this
            ->setSectionTitle(trans('plugins/chatbase::chatbase.settings.title'))
            ->setSectionDescription(trans('plugins/chatbase::chatbase.settings.description'))
            ->setValidatorClass(ChatbaseSettingRequest::class)
            ->add(
                'chatbase_api_key',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.api_key'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.api_key_helper'))
                    ->value(setting('chatbase_api_key'))
                    ->attributes([
                        'placeholder' => 'cb-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx',
                        'data-counter' => 255,
                    ])
            )
            ->add(
                'chatbase_widget_enabled',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.widget_enabled'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.widget_enabled_helper'))
                    ->value(setting('chatbase_widget_enabled', true))
            )
            ->add(
                'chatbase_auto_embed_widget',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.auto_embed_widget'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.auto_embed_widget_helper'))
                    ->value(setting('chatbase_auto_embed_widget', true))
            )
            ->add(
                'chatbase_default_model',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.default_model'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.default_model_helper'))
                    ->choices([
                        'gpt-4o-mini' => 'GPT-4o Mini',
                        'gpt-4o' => 'GPT-4o',
                        'gpt-4-turbo' => 'GPT-4 Turbo',
                        'gpt-4' => 'GPT-4',
                        'claude-3-5-sonnet' => 'Claude 3.5 Sonnet',
                        'claude-3-opus' => 'Claude 3 Opus',
                        'claude-3-haiku' => 'Claude 3 Haiku',
                        'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                        'gemini-1.5-flash' => 'Gemini 1.5 Flash',
                    ])
                    ->value(setting('chatbase_default_model', 'gpt-4o-mini'))
            )
            ->add(
                'chatbase_widget_position',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.widget_position'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.widget_position_helper'))
                    ->choices([
                        'bottom-right' => trans('plugins/chatbase::chatbase.settings.position_bottom_right'),
                        'bottom-left' => trans('plugins/chatbase::chatbase.settings.position_bottom_left'),
                        'top-right' => trans('plugins/chatbase::chatbase.settings.position_top_right'),
                        'top-left' => trans('plugins/chatbase::chatbase.settings.position_top_left'),
                    ])
                    ->value(setting('chatbase_widget_position', 'bottom-right'))
            )
            ->add(
                'chatbase_widget_theme',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.widget_theme'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.widget_theme_helper'))
                    ->choices([
                        'light' => trans('plugins/chatbase::chatbase.settings.theme_light'),
                        'dark' => trans('plugins/chatbase::chatbase.settings.theme_dark'),
                        'auto' => trans('plugins/chatbase::chatbase.settings.theme_auto'),
                    ])
                    ->value(setting('chatbase_widget_theme', 'light'))
            )
            ->add(
                'chatbase_default_instructions',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.settings.default_instructions'))
                    ->helperText(trans('plugins/chatbase::chatbase.settings.default_instructions_helper'))
                    ->value(setting('chatbase_default_instructions', ''))
                    ->rows(4)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.settings.default_instructions_placeholder'),
                    ])
            );
    }
}
