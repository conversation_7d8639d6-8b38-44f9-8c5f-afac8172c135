{"__meta": {"id": "01JYPWVDKYCR5DFMBSFQN974JB", "datetime": "2025-06-26 19:53:49", "utime": **********.439765, "method": "PUT", "uri": "/admin/plugins/status?name=chatbase", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750967619.753851, "end": **********.439788, "duration": 9.68593716621399, "duration_str": "9.69s", "measures": [{"label": "Booting", "start": 1750967619.753851, "relative_start": 0, "end": **********.659445, "relative_end": **********.659445, "duration": 0.****************, "duration_str": "906ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.659459, "relative_start": 0.****************, "end": **********.439792, "relative_end": 3.814697265625e-06, "duration": 8.***************, "duration_str": "8.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.690993, "relative_start": 0.****************, "end": **********.728078, "relative_end": **********.728078, "duration": 0.*****************, "duration_str": "37.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.432904, "relative_start": 9.**************, "end": **********.43539, "relative_end": **********.43539, "duration": 0.002485990524291992, "duration_str": "2.49ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 1174, "nb_visible_statements": 500, "nb_excluded_statements": 674, "nb_failed_statements": 0, "accumulated_duration": 4.651730000000002, "accumulated_duration_str": "4.65s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.745349, "duration": 0.01216, "duration_str": "12.16ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.261}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.770824, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.261, "width_percent": 0.01}, {"sql": "select `migration` from `migrations` order by `batch` asc, `migration` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.203582, "duration": 0.12744, "duration_str": "127ms", "memory": 0, "memory_str": null, "filename": "PluginService.php:402", "source": {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FServices%2FPluginService.php&line=402", "ajax": false, "filename": "PluginService.php", "line": "402"}, "connection": "muhrak", "explain": null, "start_percent": 0.272, "width_percent": 2.74}, {"sql": "select max(`batch`) as aggregate from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 20, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 21, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.360581, "duration": 0.01446, "duration_str": "14.46ms", "memory": 0, "memory_str": null, "filename": "PluginService.php:402", "source": {"index": 19, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FServices%2FPluginService.php&line=402", "ajax": false, "filename": "PluginService.php", "line": "402"}, "connection": "muhrak", "explain": null, "start_percent": 3.011, "width_percent": 0.311}, {"sql": "create table `chatbase_agents` (`id` bigint unsigned not null auto_increment primary key, `name` varchar(191) not null, `description` text null, `store_id` bigint unsigned not null, `customer_id` bigint unsigned not null, `chatbot_id` varchar(191) null, `status` varchar(191) not null default 'draft', `settings` json null, `training_data` json null, `error_message` text null, `last_trained_at` timestamp null, `last_synced_at` timestamp null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.710612, "duration": 0.30137, "duration_str": "301ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000001_create_chatbase_agents_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000001_create_chatbase_agents_table.php&line=11", "ajax": false, "filename": "2024_01_01_000001_create_chatbase_agents_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 3.322, "width_percent": 6.479}, {"sql": "alter table `chatbase_agents` add constraint `chatbase_agents_store_id_foreign` foreign key (`store_id`) references `mp_stores` (`id`) on delete cascade", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.01284, "duration": 0.49278, "duration_str": "493ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000001_create_chatbase_agents_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000001_create_chatbase_agents_table.php&line=11", "ajax": false, "filename": "2024_01_01_000001_create_chatbase_agents_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 9.801, "width_percent": 10.593}, {"sql": "alter table `chatbase_agents` add constraint `chatbase_agents_customer_id_foreign` foreign key (`customer_id`) references `ec_customers` (`id`) on delete cascade", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.506552, "duration": 0.24042, "duration_str": "240ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000001_create_chatbase_agents_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000001_create_chatbase_agents_table.php&line=11", "ajax": false, "filename": "2024_01_01_000001_create_chatbase_agents_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 20.394, "width_percent": 5.168}, {"sql": "alter table `chatbase_agents` add index `chatbase_agents_store_id_status_index`(`store_id`, `status`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.747846, "duration": 0.*****************, "duration_str": "261ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000001_create_chatbase_agents_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000001_create_chatbase_agents_table.php&line=11", "ajax": false, "filename": "2024_01_01_000001_create_chatbase_agents_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 25.563, "width_percent": 5.617}, {"sql": "alter table `chatbase_agents` add index `chatbase_agents_customer_id_status_index`(`customer_id`, `status`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.009933, "duration": 0.08928, "duration_str": "89.28ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000001_create_chatbase_agents_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000001_create_chatbase_agents_table.php&line=11", "ajax": false, "filename": "2024_01_01_000001_create_chatbase_agents_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 31.18, "width_percent": 1.919}, {"sql": "alter table `chatbase_agents` add unique `chatbase_agents_chatbot_id_unique`(`chatbot_id`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.10006, "duration": 0.10498, "duration_str": "105ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000001_create_chatbase_agents_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000001_create_chatbase_agents_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000001_create_chatbase_agents_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000001_create_chatbase_agents_table.php&line=11", "ajax": false, "filename": "2024_01_01_000001_create_chatbase_agents_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 33.099, "width_percent": 2.257}, {"sql": "insert into `migrations` (`migration`, `batch`) values ('2024_01_01_000001_create_chatbase_agents_table', 31)", "type": "query", "params": [], "bindings": ["2024_01_01_000001_create_chatbase_agents_table", 31], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.228838, "duration": 0.00458, "duration_str": "4.58ms", "memory": 0, "memory_str": null, "filename": "PluginService.php:402", "source": {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FServices%2FPluginService.php&line=402", "ajax": false, "filename": "PluginService.php", "line": "402"}, "connection": "muhrak", "explain": null, "start_percent": 35.356, "width_percent": 0.098}, {"sql": "create table `chatbase_training_sources` (`id` bigint unsigned not null auto_increment primary key, `agent_id` bigint unsigned not null, `type` varchar(191) not null, `title` varchar(191) not null, `content` longtext null, `url` varchar(191) null, `file_path` varchar(191) null, `status` varchar(191) not null default 'active', `character_count` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci'", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.235953, "duration": 0.05188, "duration_str": "51.88ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000002_create_chatbase_training_sources_table.php&line=11", "ajax": false, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 35.455, "width_percent": 1.115}, {"sql": "alter table `chatbase_training_sources` add constraint `chatbase_training_sources_agent_id_foreign` foreign key (`agent_id`) references `chatbase_agents` (`id`) on delete cascade", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.289279, "duration": 0.19422, "duration_str": "194ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000002_create_chatbase_training_sources_table.php&line=11", "ajax": false, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 36.57, "width_percent": 4.175}, {"sql": "alter table `chatbase_training_sources` add index `chatbase_training_sources_agent_id_type_index`(`agent_id`, `type`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4843361, "duration": 0.07523, "duration_str": "75.23ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000002_create_chatbase_training_sources_table.php&line=11", "ajax": false, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 40.745, "width_percent": 1.617}, {"sql": "alter table `chatbase_training_sources` add index `chatbase_training_sources_agent_id_status_index`(`agent_id`, `status`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, {"index": 22, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 23, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 24, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.560405, "duration": 0.033280000000000004, "duration_str": "33.28ms", "memory": 0, "memory_str": null, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php:11", "source": {"index": 13, "namespace": null, "name": "platform/plugins/chatbase/database/migrations/2024_01_01_000002_create_chatbase_training_sources_table.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\chatbase\\database\\migrations\\2024_01_01_000002_create_chatbase_training_sources_table.php", "line": 11}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fchatbase%2Fdatabase%2Fmigrations%2F2024_01_01_000002_create_chatbase_training_sources_table.php&line=11", "ajax": false, "filename": "2024_01_01_000002_create_chatbase_training_sources_table.php", "line": "11"}, "connection": "muhrak", "explain": null, "start_percent": 42.362, "width_percent": 0.715}, {"sql": "insert into `migrations` (`migration`, `batch`) values ('2024_01_01_000002_create_chatbase_training_sources_table', 31)", "type": "query", "params": [], "bindings": ["2024_01_01_000002_create_chatbase_training_sources_table", 31], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 107}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.594762, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "PluginService.php:402", "source": {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FServices%2FPluginService.php&line=402", "ajax": false, "filename": "PluginService.php", "line": "402"}, "connection": "muhrak", "explain": null, "start_percent": 43.078, "width_percent": 0.079}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, {"index": 15, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 17, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 18, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.6291149, "duration": 0.04149, "duration_str": "41.49ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:45", "source": {"index": 14, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=45", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "45"}, "connection": "muhrak", "explain": null, "start_percent": 43.157, "width_percent": 0.892}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'api_enabled'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "api_enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.673029, "duration": 0.01478, "duration_str": "14.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 44.049, "width_percent": 0.318}, {"sql": "update `settings` set `value` = '[\\\"language\\\",\\\"language-advanced\\\",\\\"ads\\\",\\\"analytics\\\",\\\"audit-log\\\",\\\"backup\\\",\\\"blog\\\",\\\"captcha\\\",\\\"contact\\\",\\\"cookie-consent\\\",\\\"ecommerce\\\",\\\"faq\\\",\\\"location\\\",\\\"mollie\\\",\\\"newsletter\\\",\\\"payment\\\",\\\"paypal\\\",\\\"paypal-payout\\\",\\\"paystack\\\",\\\"razorpay\\\",\\\"shippo\\\",\\\"simple-slider\\\",\\\"social-login\\\",\\\"sslcommerz\\\",\\\"stripe\\\",\\\"stripe-connect\\\",\\\"translation\\\",\\\"marketplace\\\",\\\"magic\\\",\\\"fob-honeypot\\\",\\\"chatbase\\\"]', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'activated_plugins'", "type": "query", "params": [], "bindings": ["[\"language\",\"language-advanced\",\"ads\",\"analytics\",\"audit-log\",\"backup\",\"blog\",\"captcha\",\"contact\",\"cookie-consent\",\"ecommerce\",\"faq\",\"location\",\"mollie\",\"newsletter\",\"payment\",\"paypal\",\"paypal-payout\",\"paystack\",\"razorpay\",\"shippo\",\"simple-slider\",\"social-login\",\"sslcommerz\",\"stripe\",\"stripe-connect\",\"translation\",\"marketplace\",\"magic\",\"fob-honeypot\",\"chatbase\"]", "2025-06-26 19:53:43", "activated_plugins"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.6899579, "duration": 0.02954, "duration_str": "29.54ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 44.366, "width_percent": 0.635}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'analytics_dashboard_widgets'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "analytics_dashboard_widgets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.720865, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.001, "width_percent": 0.083}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'enable_recaptcha_botble_contact_forms_fronts_contact_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "enable_recaptcha_botble_contact_forms_fronts_contact_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.7264788, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.085, "width_percent": 0.088}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'api_layer_api_key'", "type": "query", "params": [], "bindings": ["", "2025-06-26 19:53:43", "api_layer_api_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.73206, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.173, "width_percent": 0.087}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "enable_recaptcha_botble_newsletter_forms_fronts_newsletter_form"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.7374961, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.26, "width_percent": 0.083}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'language_hide_default'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "language_hide_default"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.7425718, "duration": 0.016579999999999998, "duration_str": "16.58ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.343, "width_percent": 0.356}, {"sql": "update `settings` set `value` = 'dropdown', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'language_switcher_display'", "type": "query", "params": [], "bindings": ["dropdown", "2025-06-26 19:53:43", "language_switcher_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.761232, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.699, "width_percent": 0.083}, {"sql": "update `settings` set `value` = 'name', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'language_display'", "type": "query", "params": [], "bindings": ["name", "2025-06-26 19:53:43", "language_display"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.76649, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.782, "width_percent": 0.079}, {"sql": "update `settings` set `value` = '[]', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'language_hide_languages'", "type": "query", "params": [], "bindings": ["[]", "2025-06-26 19:53:43", "language_hide_languages"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.772258, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.861, "width_percent": 0.091}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'simple_slider_using_assets'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "simple_slider_using_assets"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.77766, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 45.951, "width_percent": 0.079}, {"sql": "update `settings` set `value` = '932321b7adf579f4d188075350462411', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'media_random_hash'", "type": "query", "params": [], "bindings": ["932321b7adf579f4d188075350462411", "2025-06-26 19:53:43", "media_random_hash"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.7827082, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.031, "width_percent": 0.086}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'payment_cod_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "payment_cod_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.788862, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.117, "width_percent": 0.089}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'payment_bank_transfer_status'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "payment_bank_transfer_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.794361, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.206, "width_percent": 0.091}, {"sql": "update `settings` set `value` = 'muhrak', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme'", "type": "query", "params": [], "bindings": ["muhrak", "2025-06-26 19:53:43", "theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.800073, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.296, "width_percent": 0.087}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'show_admin_bar'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "show_admin_bar"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.805631, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.383, "width_percent": 0.08}, {"sql": "update `settings` set `value` = 'general/muhrak-logo.svg', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'admin_favicon'", "type": "query", "params": [], "bindings": ["general/muhrak-logo.svg", "2025-06-26 19:53:43", "admin_favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.8105881, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.462, "width_percent": 0.089}, {"sql": "update `settings` set `value` = 'general/muhrak-logo-white.svg', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'admin_logo'", "type": "query", "params": [], "bindings": ["general/muhrak-logo-white.svg", "2025-06-26 19:53:43", "admin_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.815991, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.552, "width_percent": 0.08}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'permalink-botble-blog-models-post'", "type": "query", "params": [], "bindings": ["blog", "2025-06-26 19:53:43", "permalink-botble-blog-models-post"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.821295, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.632, "width_percent": 0.092}, {"sql": "update `settings` set `value` = 'blog', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'permalink-botble-blog-models-category'", "type": "query", "params": [], "bindings": ["blog", "2025-06-26 19:53:43", "permalink-botble-blog-models-category"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.826941, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.723, "width_percent": 0.089}, {"sql": "update `settings` set `value` = 'Please pay money directly to the postman, if you choose cash on delivery method (COD).', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'payment_cod_description'", "type": "query", "params": [], "bindings": ["Please pay money directly to the postman, if you choose cash on delivery method (COD).", "2025-06-26 19:53:43", "payment_cod_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.832808, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.813, "width_percent": 0.086}, {"sql": "update `settings` set `value` = 'Please send money to our bank account: ACB - 69270 213 19.', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'payment_bank_transfer_description'", "type": "query", "params": [], "bindings": ["Please send money to our bank account: ACB - 69270 213 19.", "2025-06-26 19:53:43", "payment_bank_transfer_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.839002, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.899, "width_percent": 0.095}, {"sql": "update `settings` set `value` = 'stripe_checkout', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'payment_stripe_payment_type'", "type": "query", "params": [], "bindings": ["stripe_checkout", "2025-06-26 19:53:43", "payment_stripe_payment_type"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.844692, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 46.995, "width_percent": 0.089}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'plugins_ecommerce_customer_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "plugins_ecommerce_customer_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.85024, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.084, "width_percent": 0.092}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'plugins_ecommerce_admin_new_order_status'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "plugins_ecommerce_admin_new_order_status"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.856058, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.176, "width_percent": 0.091}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_is_enabled_support_digital_products'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "ecommerce_is_enabled_support_digital_products"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.8617158, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.267, "width_percent": 0.092}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_load_countries_states_cities_from_location_plugin'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "ecommerce_load_countries_states_cities_from_location_plugin"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.867445, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.359, "width_percent": 0.09}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'payment_bank_transfer_display_bank_info_at_the_checkout_success_page'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "payment_bank_transfer_display_bank_info_at_the_checkout_success_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.872912, "duration": 0.006030000000000001, "duration_str": "6.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.449, "width_percent": 0.13}, {"sql": "update `settings` set `value` = 'MF-2443-[%S]', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_product_sku_format'", "type": "query", "params": [], "bindings": ["MF-2443-[%S]", "2025-06-26 19:53:43", "ecommerce_product_sku_format"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.880195, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.578, "width_percent": 0.083}, {"sql": "update `settings` set `value` = '0', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_enable_product_specification'", "type": "query", "params": [], "bindings": ["0", "2025-06-26 19:53:43", "ecommerce_enable_product_specification"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.8863661, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.661, "width_percent": 0.087}, {"sql": "update `settings` set `value` = 'Martfury', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_store_name'", "type": "query", "params": [], "bindings": ["Martfury", "2025-06-26 19:53:43", "ecommerce_store_name"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.8916652, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.748, "width_percent": 0.089}, {"sql": "update `settings` set `value` = '1800979769', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_store_phone'", "type": "query", "params": [], "bindings": ["1800979769", "2025-06-26 19:53:43", "ecommerce_store_phone"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.8970609, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.837, "width_percent": 0.087}, {"sql": "update `settings` set `value` = '502 New Street', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_store_address'", "type": "query", "params": [], "bindings": ["502 New Street", "2025-06-26 19:53:43", "ecommerce_store_address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.902358, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 47.924, "width_percent": 0.089}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_store_state'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-06-26 19:53:43", "ecommerce_store_state"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.907783, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.013, "width_percent": 0.086}, {"sql": "update `settings` set `value` = 'Brighton VIC', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_store_city'", "type": "query", "params": [], "bindings": ["Brighton VIC", "2025-06-26 19:53:43", "ecommerce_store_city"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.913125, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.098, "width_percent": 0.088}, {"sql": "update `settings` set `value` = 'AU', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'ecommerce_store_country'", "type": "query", "params": [], "bindings": ["AU", "2025-06-26 19:53:43", "ecommerce_store_country"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.919183, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.186, "width_percent": 0.087}, {"sql": "update `settings` set `value` = 'Muhrak', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-site_title'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON>", "2025-06-26 19:53:43", "theme-martfury-site_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.925237, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.273, "width_percent": 0.09}, {"sql": "update `settings` set `value` = 'MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-seo_description'", "type": "query", "params": [], "bindings": ["MartFury is a clean & modern Laravel Ecommerce System for multipurpose online stores. With design clean and trendy, MartFury will make your online store look more impressive and attractive to viewers.", "2025-06-26 19:53:43", "theme-martfury-seo_description"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.930914, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.363, "width_percent": 0.087}, {"sql": "update `settings` set `value` = '© %Y MartFury. All Rights Reserved.', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-copyright'", "type": "query", "params": [], "bindings": ["© %Y MartFury. All Rights Reserved.", "2025-06-26 19:53:43", "theme-martfury-copyright"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.936277, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.45, "width_percent": 0.088}, {"sql": "update `settings` set `value` = 'general/favicon.png', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-favicon'", "type": "query", "params": [], "bindings": ["general/favicon.png", "2025-06-26 19:53:43", "theme-martfury-favicon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.9417229, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.537, "width_percent": 0.092}, {"sql": "update `settings` set `value` = 'general/logo.png', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-logo'", "type": "query", "params": [], "bindings": ["general/logo.png", "2025-06-26 19:53:43", "theme-martfury-logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.94747, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.629, "width_percent": 0.081}, {"sql": "update `settings` set `value` = 'Welcome to MartFury Online Shopping Store!', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-welcome_message'", "type": "query", "params": [], "bindings": ["Welcome to MartFury Online Shopping Store!", "2025-06-26 19:53:43", "theme-martfury-welcome_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.952502, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.71, "width_percent": 0.084}, {"sql": "update `settings` set `value` = '502 New Street, Brighton VIC, Australia', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-address'", "type": "query", "params": [], "bindings": ["502 New Street, Brighton VIC, Australia", "2025-06-26 19:53:43", "theme-martfury-address"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.957749, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.795, "width_percent": 0.084}, {"sql": "update `settings` set `value` = '1800 97 97 69', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-hotline'", "type": "query", "params": [], "bindings": ["1800 97 97 69", "2025-06-26 19:53:43", "theme-martfury-hotline"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.963028, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.879, "width_percent": 0.085}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-email'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-26 19:53:43", "theme-martfury-email"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.968215, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 48.964, "width_percent": 0.088}, {"sql": "update `settings` set `value` = 'general/newsletter.jpg', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-newsletter_image'", "type": "query", "params": [], "bindings": ["general/newsletter.jpg", "2025-06-26 19:53:43", "theme-martfury-newsletter_image"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.9747648, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.051, "width_percent": 0.077}, {"sql": "update `settings` set `value` = '1', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-homepage_id'", "type": "query", "params": [], "bindings": ["1", "2025-06-26 19:53:43", "theme-martfury-homepage_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.9796019, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.128, "width_percent": 0.08}, {"sql": "update `settings` set `value` = '6', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-blog_page_id'", "type": "query", "params": [], "bindings": ["6", "2025-06-26 19:53:43", "theme-martfury-blog_page_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.984555, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.208, "width_percent": 0.087}, {"sql": "update `settings` set `value` = 'Your experience on this site will be improved by allowing cookies ', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-cookie_consent_message'", "type": "query", "params": [], "bindings": ["Your experience on this site will be improved by allowing cookies ", "2025-06-26 19:53:43", "theme-martfury-cookie_consent_message"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.9904592, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.295, "width_percent": 0.077}, {"sql": "update `settings` set `value` = '/cookie-policy', `settings`.`updated_at` = '2025-06-26 19:53:43' where `key` = 'theme-martfury-cookie_consent_learn_more_url'", "type": "query", "params": [], "bindings": ["/cookie-policy", "2025-06-26 19:53:43", "theme-martfury-cookie_consent_learn_more_url"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": **********.996089, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.372, "width_percent": 0.084}, {"sql": "update `settings` set `value` = 'Cookie Policy', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-cookie_consent_learn_more_text'", "type": "query", "params": [], "bindings": ["<PERSON><PERSON>", "2025-06-26 19:53:44", "theme-martfury-cookie_consent_learn_more_text"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.001872, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.456, "width_percent": 0.082}, {"sql": "update `settings` set `value` = '42', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-number_of_products_per_page'", "type": "query", "params": [], "bindings": ["42", "2025-06-26 19:53:44", "theme-martfury-number_of_products_per_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.0070581, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.538, "width_percent": 0.081}, {"sql": "update `settings` set `value` = 'Shipping worldwide', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_1_title'", "type": "query", "params": [], "bindings": ["Shipping worldwide", "2025-06-26 19:53:44", "theme-martfury-product_feature_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.012196, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.619, "width_percent": 0.087}, {"sql": "update `settings` set `value` = 'icon-network', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_1_icon'", "type": "query", "params": [], "bindings": ["icon-network", "2025-06-26 19:53:44", "theme-martfury-product_feature_1_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.017892, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.707, "width_percent": 0.083}, {"sql": "update `settings` set `value` = 'Free 7-day return if eligible, so easy', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_2_title'", "type": "query", "params": [], "bindings": ["Free 7-day return if eligible, so easy", "2025-06-26 19:53:44", "theme-martfury-product_feature_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.0241718, "duration": 0.04783, "duration_str": "47.83ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 49.79, "width_percent": 1.028}, {"sql": "update `settings` set `value` = 'icon-3d-rotate', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_2_icon'", "type": "query", "params": [], "bindings": ["icon-3d-rotate", "2025-06-26 19:53:44", "theme-martfury-product_feature_2_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.074039, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 50.818, "width_percent": 0.095}, {"sql": "update `settings` set `value` = 'Supplier give bills for this product.', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_3_title'", "type": "query", "params": [], "bindings": ["Supplier give bills for this product.", "2025-06-26 19:53:44", "theme-martfury-product_feature_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.079839, "duration": 0.011269999999999999, "duration_str": "11.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 50.913, "width_percent": 0.242}, {"sql": "update `settings` set `value` = 'icon-receipt', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_3_icon'", "type": "query", "params": [], "bindings": ["icon-receipt", "2025-06-26 19:53:44", "theme-martfury-product_feature_3_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.092444, "duration": 0.13044999999999998, "duration_str": "130ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 51.156, "width_percent": 2.804}, {"sql": "update `settings` set `value` = 'Pay online or when receiving goods', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_4_title'", "type": "query", "params": [], "bindings": ["Pay online or when receiving goods", "2025-06-26 19:53:44", "theme-martfury-product_feature_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.2244148, "duration": 0.00936, "duration_str": "9.36ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 53.96, "width_percent": 0.201}, {"sql": "update `settings` set `value` = 'icon-credit-card', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-product_feature_4_icon'", "type": "query", "params": [], "bindings": ["icon-credit-card", "2025-06-26 19:53:44", "theme-martfury-product_feature_4_icon"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.235142, "duration": 0.00421, "duration_str": "4.21ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 54.161, "width_percent": 0.091}, {"sql": "update `settings` set `value` = 'Contact Directly', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_1_title'", "type": "query", "params": [], "bindings": ["Contact Directly", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_1_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.2414, "duration": 0.013, "duration_str": "13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 54.252, "width_percent": 0.279}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_1_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_1_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.2557688, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 54.531, "width_percent": 0.089}, {"sql": "update `settings` set `value` = '(+004) 912-3548-07', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_1_details'", "type": "query", "params": [], "bindings": ["(+004) 912-3548-07", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_1_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.261369, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 54.62, "width_percent": 0.078}, {"sql": "update `settings` set `value` = 'Headquarters', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_2_title'", "type": "query", "params": [], "bindings": ["Headquarters", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_2_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.266638, "duration": 0.02524, "duration_str": "25.24ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 54.698, "width_percent": 0.543}, {"sql": "update `settings` set `value` = '17 Queen St, South bank, Melbourne 10560, Australia', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_2_subtitle'", "type": "query", "params": [], "bindings": ["17 Queen St, South bank, Melbourne 10560, Australia", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_2_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.293145, "duration": 0.01832, "duration_str": "18.32ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 55.241, "width_percent": 0.394}, {"sql": "update `settings` set `value` = '', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_2_details'", "type": "query", "params": [], "bindings": ["", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_2_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.312616, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 55.635, "width_percent": 0.078}, {"sql": "update `settings` set `value` = 'Work With Us', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_3_title'", "type": "query", "params": [], "bindings": ["Work With Us", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_3_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.317311, "duration": 0.00932, "duration_str": "9.32ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 55.712, "width_percent": 0.2}, {"sql": "update `settings` set `value` = 'Send your CV to our email:', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_3_subtitle'", "type": "query", "params": [], "bindings": ["Send your CV to our email:", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_3_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.327952, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 55.913, "width_percent": 0.083}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_3_details'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_3_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.333228, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 55.995, "width_percent": 0.082}, {"sql": "update `settings` set `value` = 'Customer Service', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_4_title'", "type": "query", "params": [], "bindings": ["Customer Service", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_4_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.338438, "duration": 0.02945, "duration_str": "29.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 56.077, "width_percent": 0.633}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_4_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_4_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.369724, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 56.711, "width_percent": 0.083}, {"sql": "update `settings` set `value` = '(800) 843-2446', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_4_details'", "type": "query", "params": [], "bindings": ["(800) 843-2446", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_4_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.3748891, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 56.794, "width_percent": 0.089}, {"sql": "update `settings` set `value` = 'Media Relations', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_5_title'", "type": "query", "params": [], "bindings": ["Media Relations", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_5_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.381007, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 56.883, "width_percent": 0.087}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_5_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_5_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.387025, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 56.97, "width_percent": 0.112}, {"sql": "update `settings` set `value` = '(801) 947-3564', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_5_details'", "type": "query", "params": [], "bindings": ["(801) 947-3564", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_5_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.3934891, "duration": 0.006059999999999999, "duration_str": "6.06ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 57.083, "width_percent": 0.13}, {"sql": "update `settings` set `value` = 'Vendor Support', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_6_title'", "type": "query", "params": [], "bindings": ["Vendor Support", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_6_title"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.400832, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 57.213, "width_percent": 0.095}, {"sql": "update `settings` set `value` = '<EMAIL>', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_6_subtitle'", "type": "query", "params": [], "bindings": ["<EMAIL>", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_6_subtitle"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.407381, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 57.308, "width_percent": 0.089}, {"sql": "update `settings` set `value` = '(801) 947-3100', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-contact_info_box_6_details'", "type": "query", "params": [], "bindings": ["(801) 947-3100", "2025-06-26 19:53:44", "theme-martfury-contact_info_box_6_details"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.4127889, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 57.396, "width_percent": 0.092}, {"sql": "update `settings` set `value` = '7', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-number_of_cross_sale_product'", "type": "query", "params": [], "bindings": ["7", "2025-06-26 19:53:44", "theme-martfury-number_of_cross_sale_product"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.41847, "duration": 0.030789999999999998, "duration_str": "30.79ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 57.488, "width_percent": 0.662}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-logo_in_the_checkout_page'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-06-26 19:53:44", "theme-martfury-logo_in_the_checkout_page"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.450581, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 58.15, "width_percent": 0.094}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-logo_in_invoices'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-06-26 19:53:44", "theme-martfury-logo_in_invoices"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.457073, "duration": 0.01009, "duration_str": "10.09ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 58.244, "width_percent": 0.217}, {"sql": "update `settings` set `value` = 'general/logo-dark.png', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-logo_vendor_dashboard'", "type": "query", "params": [], "bindings": ["general/logo-dark.png", "2025-06-26 19:53:44", "theme-martfury-logo_vendor_dashboard"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.469217, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 58.461, "width_percent": 0.086}, {"sql": "update `settings` set `value` = 'Work Sans', `settings`.`updated_at` = '2025-06-26 19:53:44' where `key` = 'theme-martfury-primary_font'", "type": "query", "params": [], "bindings": ["Work Sans", "2025-06-26 19:53:44", "theme-martfury-primary_font"], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, {"index": 13, "namespace": null, "name": "platform/core/setting/src/Supports/SettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\SettingStore.php", "line": 102}, {"index": 14, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 376}, {"index": 15, "namespace": null, "name": "platform/packages/plugin-management/src/Services/PluginService.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Services\\PluginService.php", "line": 120}, {"index": 16, "namespace": null, "name": "platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\plugin-management\\src\\Http\\Controllers\\PluginManagementController.php", "line": 108}], "start": 1750967624.474752, "duration": 0.14553, "duration_str": "146ms", "memory": 0, "memory_str": null, "filename": "DatabaseSettingStore.php:55", "source": {"index": 12, "namespace": null, "name": "platform/core/setting/src/Supports/DatabaseSettingStore.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\setting\\src\\Supports\\DatabaseSettingStore.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fsetting%2Fsrc%2FSupports%2FDatabaseSettingStore.php&line=55", "ajax": false, "filename": "DatabaseSettingStore.php", "line": "55"}, "connection": "muhrak", "explain": null, "start_percent": 58.547, "width_percent": 3.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.622001, "duration": 0.02835, "duration_str": "28.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.675, "width_percent": 0.609}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.6508331, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.285, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.655056, "duration": 0.08059999999999999, "duration_str": "80.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.368, "width_percent": 1.733}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.736126, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.1, "width_percent": 0.096}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.741144, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.196, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.745686, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.284, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.750206, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.375, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.754419, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.46, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.758752, "duration": 0.00965, "duration_str": "9.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.545, "width_percent": 0.207}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.768961, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.753, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.773557, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.84, "width_percent": 0.097}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.778591, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 64.937, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.783051, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.025, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.787379, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.111, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.791808, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.199, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.795974, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.282, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.799987, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.363, "width_percent": 0.082}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.80412, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.445, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.80861, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.53, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.8130171, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.615, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.8172538, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.697, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.821889, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.789, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.826384, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.877, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.83105, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.967, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.8355098, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.052, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.839791, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.136, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.844092, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.223, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.848289, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.306, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.852894, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.393, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.857409, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.482, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.861755, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.566, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.866056, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.649, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.8703358, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.735, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.8746262, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.819, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.879068, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.905, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.883344, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 66.989, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.887795, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.077, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.8922982, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.167, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.896535, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.252, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.901098, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.344, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.905428, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.43, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.909787, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.517, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.914052, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.601, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.91822, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.684, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.92264, "duration": 0.00541, "duration_str": "5.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.769, "width_percent": 0.116}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.92841, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.885, "width_percent": 0.094}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.933157, "duration": 0.00715, "duration_str": "7.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 67.979, "width_percent": 0.154}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.940714, "duration": 0.00868, "duration_str": "8.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.133, "width_percent": 0.187}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.9499002, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.32, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.954179, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.406, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.958679, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.495, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.963076, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.583, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.9674008, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.668, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.9716039, "duration": 0.00444, "duration_str": "4.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.753, "width_percent": 0.095}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.9763892, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.848, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.9808462, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.937, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.985252, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.027, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.989657, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.114, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.99403, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.201, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967624.998282, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.286, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.002668, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.374, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.007324, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.465, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.011612, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.551, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.0162048, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.639, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.020429, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.722, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.024721, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.805, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.029064, "duration": 0.01271, "duration_str": "12.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.89, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.0421128, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.163, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.04662, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.252, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.0511029, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.336, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.05571, "duration": 0.0044, "duration_str": "4.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.424, "width_percent": 0.095}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.0604768, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.518, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.065109, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.611, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.0696368, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.7, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.074147, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.788, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.078802, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.877, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.083451, "duration": 0.00526, "duration_str": "5.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.967, "width_percent": 0.113}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.089263, "duration": 0.00463, "duration_str": "4.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.08, "width_percent": 0.1}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.094482, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.18, "width_percent": 0.102}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.099585, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.282, "width_percent": 0.095}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1043499, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.377, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1086118, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.461, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.113008, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.549, "width_percent": 0.082}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.117123, "duration": 0.02077, "duration_str": "20.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.631, "width_percent": 0.447}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.138253, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.078, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1426811, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.164, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.147359, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.257, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.15182, "duration": 0.00437, "duration_str": "4.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.344, "width_percent": 0.094}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1566901, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.438, "width_percent": 0.097}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.161585, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.536, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1662488, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.625, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.170942, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.717, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1757739, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.81, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1803908, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.898, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1848, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.981, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.189365, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.07, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.193976, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.161, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.1985788, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.248, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.203008, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.336, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2077491, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.424, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.212081, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.511, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.216477, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.599, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.22086, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.687, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.225229, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.774, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.229793, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.866, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.234129, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.953, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2383091, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.037, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.242979, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.126, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.247742, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.219, "width_percent": 0.094}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.252501, "duration": 0.00442, "duration_str": "4.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.313, "width_percent": 0.095}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2572901, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.408, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.261735, "duration": 0.00826, "duration_str": "8.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.494, "width_percent": 0.178}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2703521, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.671, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2750292, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.76, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.27941, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.846, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.283054, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.919, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2867742, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.994, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2907128, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.067, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.294862, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.148, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.2986002, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.223, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3023279, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.297, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.306148, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.369, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.310283, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.448, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.313976, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.52, "width_percent": 0.07}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.317637, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.59, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.321369, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.662, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.325104, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.735, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3291242, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.814, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.333182, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.891, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.336842, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 75.963, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.341133, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.041, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.345253, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.119, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3490891, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.195, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.352915, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.27, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3569012, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.348, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.360818, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.425, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3644571, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.499, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.36815, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.573, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.371752, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.645, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.375592, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.72, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.379369, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.796, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.383376, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.875, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3872402, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 76.948, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.3912518, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.025, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.394929, "duration": 0.0034100000000000003, "duration_str": "3.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.098, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.398722, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.172, "width_percent": 0.069}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.402215, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.241, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.406083, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.316, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4100199, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.393, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4136078, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.465, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.417243, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.538, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.421057, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.613, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.425056, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.69, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4289389, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.768, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.432842, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.841, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.436965, "duration": 0.00771, "duration_str": "7.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 77.92, "width_percent": 0.166}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.445026, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.086, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.449357, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.17, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4531622, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.244, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4573681, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.322, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.461256, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.4, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4649289, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.473, "width_percent": 0.069}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.468407, "duration": 0.0035099999999999997, "duration_str": "3.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.543, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.472255, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.618, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.4762712, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.694, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.480016, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.769, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.483927, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.843, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.487871, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.922, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.491817, "duration": 0.00434, "duration_str": "4.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.998, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.496486, "duration": 0.00547, "duration_str": "5.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.092, "width_percent": 0.118}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.502498, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.209, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.507152, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.297, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5111709, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.375, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.51528, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.455, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.519601, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.535, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.523914, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.614, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.528149, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.697, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5322258, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.777, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.53642, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.858, "width_percent": 0.082}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5408502, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.94, "width_percent": 0.082}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.545156, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.022, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.549438, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.102, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.55369, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.184, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.557919, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.263, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5619872, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.343, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.56619, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.424, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.570594, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.509, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.575334, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.599, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5797038, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.686, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5841718, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.774, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5884838, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.859, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.592849, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.943, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.5974119, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.03, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.601768, "duration": 0.00432, "duration_str": "4.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.115, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6066859, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.207, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6114562, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.299, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6162949, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.392, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.620972, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.481, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.625548, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.569, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6298628, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.653, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.634197, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.738, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6385732, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.823, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.643288, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.912, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.647802, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.999, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.652373, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.088, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.656876, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.171, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.661528, "duration": 0.00415, "duration_str": "4.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.261, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.666296, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.35, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.670861, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.438, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.675158, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.522, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6794498, "duration": 0.0040999999999999995, "duration_str": "4.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.606, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.6839302, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.694, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.688435, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.783, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.69279, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.869, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.697069, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.955, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.701129, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.035, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.705532, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.118, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.709888, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.203, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.714329, "duration": 0.00431, "duration_str": "4.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.288, "width_percent": 0.093}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.7192109, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.381, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.7235959, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.464, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.728239, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.552, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.733, "duration": 0.00409, "duration_str": "4.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.642, "width_percent": 0.088}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.737457, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.73, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.741838, "duration": 0.00424, "duration_str": "4.24ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.813, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.746508, "duration": 0.01477, "duration_str": "14.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.904, "width_percent": 0.318}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.761664, "duration": 0.0059900000000000005, "duration_str": "5.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.222, "width_percent": 0.129}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.7681699, "duration": 0.006030000000000001, "duration_str": "6.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.351, "width_percent": 0.13}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.774761, "duration": 0.00833, "duration_str": "8.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.48, "width_percent": 0.179}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.783612, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.659, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.787534, "duration": 0.01268, "duration_str": "12.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.736, "width_percent": 0.273}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.800529, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.008, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.804265, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.082, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.8080301, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.155, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.811843, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.229, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.815618, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.301, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.819711, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.379, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.8239741, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.459, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.828504, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.549, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.832218, "duration": 0.0037400000000000003, "duration_str": "3.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.623, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.83642, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.703, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.840456, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.778, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.84512, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.867, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.849108, "duration": 0.00333, "duration_str": "3.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.944, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.852746, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.015, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.8565989, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.089, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.860863, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.17, "width_percent": 0.106}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.8662279, "duration": 0.0048, "duration_str": "4.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.276, "width_percent": 0.103}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.871372, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.379, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.875612, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.462, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.880048, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.547, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.884545, "duration": 0.00967, "duration_str": "9.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.631, "width_percent": 0.208}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.8946269, "duration": 0.01582, "duration_str": "15.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.839, "width_percent": 0.34}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.910846, "duration": 0.00891, "duration_str": "8.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.179, "width_percent": 0.192}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.920323, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.371, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.924489, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.45, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.928362, "duration": 0.00618, "duration_str": "6.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.526, "width_percent": 0.133}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.934864, "duration": 0.0074199999999999995, "duration_str": "7.42ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.658, "width_percent": 0.16}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.942616, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.818, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.946421, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.893, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.950051, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.965, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.953732, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.038, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.957703, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.113, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.961614, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.19, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.96522, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.262, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.9688501, "duration": 0.00332, "duration_str": "3.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.335, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.9726, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.406, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.9765701, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.483, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.980503, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.558, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.984302, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.633, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.988161, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.705, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.992547, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.792, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967625.996562, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.868, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0004542, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.946, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.00424, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.02, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.008235, "duration": 0.00392, "duration_str": "3.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.099, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.01253, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.183, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.016639, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.262, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0204098, "duration": 0.0044599999999999996, "duration_str": "4.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.336, "width_percent": 0.096}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.025285, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.432, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.029258, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.511, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0331728, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.589, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0369349, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.662, "width_percent": 0.096}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0419111, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.757, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.04604, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.835, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.049873, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.911, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.053692, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.985, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.057853, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.063, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0621932, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.146, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.066426, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.228, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.0704489, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.304, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.074705, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.384, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.078695, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.46, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.082535, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.534, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.086543, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.612, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.090532, "duration": 0.00367, "duration_str": "3.67ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.686, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.094575, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.765, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.098605, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.84, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.102587, "duration": 0.010320000000000001, "duration_str": "10.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.918, "width_percent": 0.222}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.113448, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.139, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.11776, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.22, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.121794, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.295, "width_percent": 0.078}, {"sql": "select `key` from `settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.1457028, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.373, "width_percent": 0.024}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.147874, "duration": 0.01018, "duration_str": "10.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.397, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.158521, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.616, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.1626189, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.697, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.166528, "duration": 0.00344, "duration_str": "3.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.774, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.170342, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.848, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.174478, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.925, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.178551, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.005, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.182209, "duration": 0.00337, "duration_str": "3.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.077, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.185961, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.149, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.1898851, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.221, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.194223, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.302, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.198151, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.375, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.2019331, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.449, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.2058032, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.523, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.21, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.602, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.2143722, "duration": 0.01117, "duration_str": "11.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.685, "width_percent": 0.24}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.2260458, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.925, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.229992, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.002, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.233974, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.079, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.237945, "duration": 0.00528, "duration_str": "5.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.153, "width_percent": 0.114}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.243652, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.266, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.247488, "duration": 0.0034300000000000003, "duration_str": "3.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.341, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.251277, "duration": 0.00404, "duration_str": "4.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.415, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.2557871, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.502, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.259953, "duration": 0.01192, "duration_str": "11.92ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.58, "width_percent": 0.256}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.2723238, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.836, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.27656, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.917, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.280554, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.991, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.284585, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.067, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.288605, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.142, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.292825, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.22, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.29697, "duration": 0.0034, "duration_str": "3.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.298, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3007789, "duration": 0.0033900000000000002, "duration_str": "3.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.371, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.304564, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.444, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3086288, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.519, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.313054, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.603, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3170311, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.679, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.320887, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.754, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.325116, "duration": 0.00369, "duration_str": "3.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.832, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3293009, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.912, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3332522, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.989, "width_percent": 0.074}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.337184, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.063, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.341191, "duration": 0.00363, "duration_str": "3.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.138, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.345162, "duration": 0.0033799999999999998, "duration_str": "3.38ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.216, "width_percent": 0.073}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3490121, "duration": 0.01134, "duration_str": "11.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.288, "width_percent": 0.244}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3608892, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.532, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.365184, "duration": 0.010199999999999999, "duration_str": "10.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.61, "width_percent": 0.219}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3760169, "duration": 0.00393, "duration_str": "3.93ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.829, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.380518, "duration": 0.00423, "duration_str": "4.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.913, "width_percent": 0.091}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.385273, "duration": 0.00387, "duration_str": "3.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.004, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.389525, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.087, "width_percent": 0.086}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.393897, "duration": 0.00378, "duration_str": "3.78ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.174, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.3980048, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.255, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.402138, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.336, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4063, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.416, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4107358, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.503, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4151049, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.586, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.419401, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.67, "width_percent": 0.079}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4235392, "duration": 0.0039700000000000004, "duration_str": "3.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.749, "width_percent": 0.085}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4278948, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.835, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.432334, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.918, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.436591, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.002, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.440842, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.086, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.445114, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.169, "width_percent": 0.081}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.449461, "duration": 0.0038399999999999997, "duration_str": "3.84ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.25, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.45367, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.333, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4578578, "duration": 0.0042699999999999995, "duration_str": "4.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.413, "width_percent": 0.092}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4624698, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.505, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.4664059, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.582, "width_percent": 0.083}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.470665, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.665, "width_percent": 0.084}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.474982, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.749, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.479369, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.836, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.483464, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.916, "width_percent": 0.089}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.488059, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.005, "width_percent": 0.087}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.492615, "duration": 0.00418, "duration_str": "4.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.091, "width_percent": 0.09}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.497177, "duration": 0.00495, "duration_str": "4.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.181, "width_percent": 0.106}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.502545, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.288, "width_percent": 0.075}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.506568, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.362, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.5107532, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.441, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.51493, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.518, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.519232, "duration": 0.00811, "duration_str": "8.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.599, "width_percent": 0.174}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.5277112, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.773, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.531726, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.85, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.535965, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.93, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.540154, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.01, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.5441072, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.086, "width_percent": 0.078}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.5482888, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.164, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.5523548, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.24, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.556376, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.317, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.560322, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.394, "width_percent": 0.072}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.564032, "duration": 0.00329, "duration_str": "3.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.466, "width_percent": 0.071}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.567863, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.537, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.5720139, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.613, "width_percent": 0.077}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.576253, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.69, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.580441, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.77, "width_percent": 0.076}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.584357, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.846, "width_percent": 0.08}, {"sql": "update `settings` set `value` = ?, `settings`.`updated_at` = ? where `key` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1750967626.588592, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.926, "width_percent": 0.074}, {"sql": "... 674 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/plugins/status?name=chatbase", "action_name": "plugins.change.status", "controller_action": "Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController@update", "uri": "PUT admin/plugins/status", "permission": "plugins.index", "controller": "Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController@update<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php&line=93\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\PluginManagement\\Http\\Controllers", "prefix": "admin/plugins", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fpackages%2Fplugin-management%2Fsrc%2FHttp%2FControllers%2FPluginManagementController.php&line=93\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/packages/plugin-management/src/Http/Controllers/PluginManagementController.php:93-130</a>", "middleware": "web, core, auth, preventDemo", "duration": "9.73s", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-140907005 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">chatbase</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140907005\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1007868469 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">put</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007868469\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1875724453 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImhOeTlRT0FvMlBPQ3ZGQU1iVlRUVWc9PSIsInZhbHVlIjoiOEpDRWk1dVVKZGE2cGNwdFVsOUVDSFhPL3R0dG9LbTlsVXZtVE9RaWt5UWZyaWoxSFdlK3lBWmFDVTF2MFBtOGFhYmxsTmx4eGxGbk5VZlplamRGdXh1TVFnNW5IKzlZbEI4K1dqeXpIV21xbWRtdkNjb2Job1U5Zk8wSngxTW0iLCJtYWMiOiI0OTdhMzBjNTBhMTI2ODk2ODA0ZTRiNzIwOTM3M2VlOTk4OTZmMjIxMzlkZTZjNmYyYzg0YTA3N2FiYWE4OWY2IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">17</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">https://muhrak.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImhOeTlRT0FvMlBPQ3ZGQU1iVlRUVWc9PSIsInZhbHVlIjoiOEpDRWk1dVVKZGE2cGNwdFVsOUVDSFhPL3R0dG9LbTlsVXZtVE9RaWt5UWZyaWoxSFdlK3lBWmFDVTF2MFBtOGFhYmxsTmx4eGxGbk5VZlplamRGdXh1TVFnNW5IKzlZbEI4K1dqeXpIV21xbWRtdkNjb2Job1U5Zk8wSngxTW0iLCJtYWMiOiI0OTdhMzBjNTBhMTI2ODk2ODA0ZTRiNzIwOTM3M2VlOTk4OTZmMjIxMzlkZTZjNmYyYzg0YTA3N2FiYWE4OWY2IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjZIVXZ1QlhtZWxGOG9tVEhoUGVUTUE9PSIsInZhbHVlIjoieHFhNVVKcEN2MXJ3emJzZk40dUNacytUVzFGT1RvM2VFYWt0ZHpXNXc2WTJlQ2MxdUlyRFdRNDBrZXN3Y3paTS9JQ3pBVU9ZKytQejhPSURSQndJcGhxS3ZrenBBbkhZeVVJaXN1VDZqWEY1dlQ0LzJvZUpSb25lYXNQUW9rVnUiLCJtYWMiOiI2MTI0MGVjNDc3OGQxYTJlYTNiNjM3MGZmYjRkMTgzODQ0N2E5ZDhlYjE4MDViYjU4MzJiZTA2M2U2YjJmYmM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875724453\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZTyZzJYnF22t7azPqhiPtmm2ENvEZF1HcWlYj04q</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LJpKBb57DxOE28u17ev3T58Vzs01SmjtA73qRamn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-463796650 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 26 Jun 2025 19:53:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463796650\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-973621534 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZTyZzJYnF22t7azPqhiPtmm2ENvEZF1HcWlYj04q</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"41 characters\">https://muhrak.gc/admin/plugins/installed</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"18 characters\">https://muhrak.gc/</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-973621534\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/plugins/status?name=chatbase", "action_name": "plugins.change.status", "controller_action": "Botble\\PluginManagement\\Http\\Controllers\\PluginManagementController@update"}, "badge": null}}