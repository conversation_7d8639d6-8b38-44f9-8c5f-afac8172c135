<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Bo<PERSON>ble\Base\Http\Controllers\BaseController;
use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Chatbase\Forms\TrainingSourceForm;
use Bo<PERSON>ble\Chatbase\Http\Requests\TrainingSourceRequest;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Models\ChatbaseTrainingSource;
use Illuminate\Http\Request;

class TrainingSourceController extends BaseController
{
    public function store(string $agentId, TrainingSourceRequest $request): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $agentId)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $source = ChatbaseTrainingSource::create([
            'agent_id' => $agent->id,
            'type' => $request->input('type'),
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'url' => $request->input('url'),
            'status' => 'active',
        ]);

        $source->updateCharacterCount();

        return $this
            ->httpResponse()
            ->setMessage(__('Training source added successfully!'));
    }

    public function update(string $agentId, string $sourceId, TrainingSourceRequest $request): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $agentId)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $source = ChatbaseTrainingSource::where('id', $sourceId)
            ->where('agent_id', $agent->id)
            ->firstOrFail();

        $source->update([
            'title' => $request->input('title'),
            'content' => $request->input('content'),
            'url' => $request->input('url'),
        ]);

        $source->updateCharacterCount();

        return $this
            ->httpResponse()
            ->setMessage(__('Training source updated successfully!'));
    }

    public function destroy(string $agentId, string $sourceId): BaseHttpResponse
    {
        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $agentId)
            ->where('store_id', $store->id)
            ->firstOrFail();

        $source = ChatbaseTrainingSource::where('id', $sourceId)
            ->where('agent_id', $agent->id)
            ->firstOrFail();

        $source->delete();

        return $this
            ->httpResponse()
            ->setMessage(__('Training source deleted successfully!'));
    }
}
