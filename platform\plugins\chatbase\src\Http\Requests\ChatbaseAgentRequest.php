<?php

namespace Botble\Chatbase\Http\Requests;

use Botble\Support\Http\Requests\Request;

class ChatbaseAgentRequest extends Request
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'training_text' => ['nullable', 'string', 'max:50000'],
            'model' => ['nullable', 'string', 'in:gpt-4o-mini,gpt-4o,gpt-4-turbo,gpt-4,claude-3-5-sonnet,claude-3-opus,claude-3-haiku,gemini-1.5-pro,gemini-1.5-flash'],
            'temperature' => ['nullable', 'string', 'in:0,0.3,0.7,1'],
            'widget_enabled' => ['nullable', 'boolean'],
            'widget_position' => ['nullable', 'string', 'in:bottom-right,bottom-left,top-right,top-left'],
            'widget_theme' => ['nullable', 'string', 'in:light,dark,auto'],
            'custom_instructions' => ['nullable', 'string', 'max:2000'],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => trans('plugins/chatbase::chatbase.agent.name'),
            'description' => trans('plugins/chatbase::chatbase.agent.description'),
            'training_text' => trans('plugins/chatbase::chatbase.agent.training_text'),
            'model' => trans('plugins/chatbase::chatbase.agent.model'),
            'temperature' => trans('plugins/chatbase::chatbase.agent.temperature'),
            'widget_enabled' => trans('plugins/chatbase::chatbase.agent.widget_enabled'),
            'widget_position' => trans('plugins/chatbase::chatbase.agent.widget_position'),
            'widget_theme' => trans('plugins/chatbase::chatbase.agent.widget_theme'),
            'custom_instructions' => trans('plugins/chatbase::chatbase.agent.custom_instructions'),
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'widget_enabled' => $this->boolean('widget_enabled'),
        ]);
    }

    public function validated($key = null, $default = null): array
    {
        $validated = parent::validated($key, $default);

        // Prepare settings array
        $settings = [
            'model' => $validated['model'] ?? setting('chatbase_default_model', 'gpt-4o-mini'),
            'temperature' => (float) ($validated['temperature'] ?? 0),
            'widget' => [
                'enabled' => $validated['widget_enabled'] ?? true,
                'position' => $validated['widget_position'] ?? setting('chatbase_widget_position', 'bottom-right'),
                'theme' => $validated['widget_theme'] ?? setting('chatbase_widget_theme', 'light'),
            ],
            'instructions' => $validated['custom_instructions'] ?? '',
        ];

        $validated['settings'] = $settings;

        return $validated;
    }
}
