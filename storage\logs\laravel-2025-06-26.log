[2025-06-26 16:44:06] localhost.ERROR: finfo_file(D:\laragon\www\muhrak\public\storage\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory {"userId":1,"exception":"[object] (ErrorException(code: 0): finfo_file(D:\\laragon\\www\\muhrak\\public\\storage\\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory at D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:471)
[stacktrace]
#0 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#2 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(471): finfo_file(Object(finfo), 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Filesystem\\Filesystem->mimeType('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): Illuminate\\Support\\Facades\\Facade::__callStatic('mimeType', Array)
#5 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(781): Illuminate\\Filesystem\\Filesystem::{closure}()
#6 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): rescue(Object(Closure), 'image/x-icon')
#7 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#8 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#10 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#13 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#14 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(843): Illuminate\\View\\View->render()
#15 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->header()
#16 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\06b7c83bcd3880a29c8bd3ab9cee515f.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('header', Array)
#17 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header-meta', 'theme.muhrak::p...', Array)
#26 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header-meta')
#27 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\a5eb6bda54d32602bade0b9f4bde5ca8.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#28 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#29 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#30 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#32 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#33 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#34 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#35 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#36 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header', 'theme.muhrak::p...', Array)
#37 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header')
#38 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\08bd36177a1caaa3e770f9b1530bcd09.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#39 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#40 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#41 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#42 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#43 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#44 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#45 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#46 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(794): Illuminate\\View\\View->render()
#47 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(33): Botble\\Theme\\Theme->render()
#48 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#49 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('getIndex', Array)
#50 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getIndex')
#51 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#52 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#53 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\muhrak\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#85 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#94 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#107 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#108 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#119 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#120 D:\\laragon\\www\\muhrak\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#121 {main}
"} 
[2025-06-26 16:58:00] localhost.ERROR: finfo_file(D:\laragon\www\muhrak\public\storage\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory {"userId":1,"exception":"[object] (ErrorException(code: 0): finfo_file(D:\\laragon\\www\\muhrak\\public\\storage\\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory at D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:471)
[stacktrace]
#0 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#2 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(471): finfo_file(Object(finfo), 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Filesystem\\Filesystem->mimeType('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): Illuminate\\Support\\Facades\\Facade::__callStatic('mimeType', Array)
#5 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(781): Illuminate\\Filesystem\\Filesystem::{closure}()
#6 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): rescue(Object(Closure), 'image/x-icon')
#7 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#8 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#10 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#13 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#14 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(843): Illuminate\\View\\View->render()
#15 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->header()
#16 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\06b7c83bcd3880a29c8bd3ab9cee515f.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('header', Array)
#17 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header-meta', 'theme.muhrak::p...', Array)
#26 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header-meta')
#27 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\a5eb6bda54d32602bade0b9f4bde5ca8.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#28 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#29 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#30 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#32 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#33 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#34 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#35 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#36 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header', 'theme.muhrak::p...', Array)
#37 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header')
#38 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\eeb44d511fa0e5044fd5eb04e92ce32b.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#39 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#40 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#41 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#42 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#43 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#44 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#45 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#46 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(794): Illuminate\\View\\View->render()
#47 D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Http\\Controllers\\Fronts\\PublicStoreController.php(172): Botble\\Theme\\Theme->render()
#48 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Marketplace\\Http\\Controllers\\Fronts\\PublicStoreController->getStore('hironic', Object(Illuminate\\Http\\Request), Object(Botble\\Ecommerce\\Services\\Products\\GetProductService))
#49 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('getStore', Array)
#50 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Marketplace\\Http\\Controllers\\Fronts\\PublicStoreController), 'getStore')
#51 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#52 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#53 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\muhrak\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#85 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#94 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#107 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#108 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#119 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#120 D:\\laragon\\www\\muhrak\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#121 {main}
"} 
[2025-06-26 19:43:56] localhost.ERROR: finfo_file(D:\laragon\www\muhrak\public\storage\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory {"userId":1,"exception":"[object] (ErrorException(code: 0): finfo_file(D:\\laragon\\www\\muhrak\\public\\storage\\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory at D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:471)
[stacktrace]
#0 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#2 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(471): finfo_file(Object(finfo), 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Filesystem\\Filesystem->mimeType('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): Illuminate\\Support\\Facades\\Facade::__callStatic('mimeType', Array)
#5 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(781): Illuminate\\Filesystem\\Filesystem::{closure}()
#6 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): rescue(Object(Closure), 'image/x-icon')
#7 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#8 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#10 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#13 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#14 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(843): Illuminate\\View\\View->render()
#15 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->header()
#16 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\06b7c83bcd3880a29c8bd3ab9cee515f.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('header', Array)
#17 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header-meta', 'theme.muhrak::p...', Array)
#26 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header-meta')
#27 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\a5eb6bda54d32602bade0b9f4bde5ca8.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#28 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#29 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#30 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#32 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#33 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#34 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#35 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#36 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header', 'theme.muhrak::p...', Array)
#37 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header')
#38 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\08bd36177a1caaa3e770f9b1530bcd09.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#39 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#40 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#41 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#42 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#43 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#44 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#45 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#46 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(794): Illuminate\\View\\View->render()
#47 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(33): Botble\\Theme\\Theme->render()
#48 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#49 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('getIndex', Array)
#50 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getIndex')
#51 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#52 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#53 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\muhrak\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#85 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#94 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#107 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#108 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#119 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#120 D:\\laragon\\www\\muhrak\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#121 {main}
"} 
[2025-06-26 19:50:52] localhost.ERROR: finfo_file(D:\laragon\www\muhrak\public\storage\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory {"userId":1,"exception":"[object] (ErrorException(code: 0): finfo_file(D:\\laragon\\www\\muhrak\\public\\storage\\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory at D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:471)
[stacktrace]
#0 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#2 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(471): finfo_file(Object(finfo), 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Filesystem\\Filesystem->mimeType('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): Illuminate\\Support\\Facades\\Facade::__callStatic('mimeType', Array)
#5 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(781): Illuminate\\Filesystem\\Filesystem::{closure}()
#6 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): rescue(Object(Closure), 'image/x-icon')
#7 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#8 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#10 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#13 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#14 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(843): Illuminate\\View\\View->render()
#15 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->header()
#16 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\06b7c83bcd3880a29c8bd3ab9cee515f.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('header', Array)
#17 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header-meta', 'theme.muhrak::p...', Array)
#26 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header-meta')
#27 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\a5eb6bda54d32602bade0b9f4bde5ca8.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#28 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#29 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#30 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#32 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#33 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#34 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#35 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#36 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header', 'theme.muhrak::p...', Array)
#37 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header')
#38 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\08bd36177a1caaa3e770f9b1530bcd09.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#39 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#40 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#41 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#42 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#43 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#44 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#45 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#46 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(794): Illuminate\\View\\View->render()
#47 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Http\\Controllers\\PublicController.php(33): Botble\\Theme\\Theme->render()
#48 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Theme\\Http\\Controllers\\PublicController->getIndex()
#49 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('getIndex', Array)
#50 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Theme\\Http\\Controllers\\PublicController), 'getIndex')
#51 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#52 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#53 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#60 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\muhrak\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#85 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#86 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#87 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#92 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#94 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#96 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#99 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#104 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#105 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#107 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#108 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#119 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#120 D:\\laragon\\www\\muhrak\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#121 {main}
"} 
[2025-06-26 19:56:50] localhost.ERROR: finfo_file(D:\laragon\www\muhrak\public\storage\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory {"userId":1,"exception":"[object] (ErrorException(code: 0): finfo_file(D:\\laragon\\www\\muhrak\\public\\storage\\general/whatsapp-image-2024-10-02-at-101443-pm.jpeg): Failed to open stream: No such file or directory at D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php:471)
[stacktrace]
#0 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'finfo_file(D:\\\\l...', 'D:\\\\laragon\\\\www\\\\...', 471)
#2 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(471): finfo_file(Object(finfo), 'D:\\\\laragon\\\\www\\\\...')
#3 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Filesystem\\Filesystem->mimeType('D:\\\\laragon\\\\www\\\\...')
#4 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): Illuminate\\Support\\Facades\\Facade::__callStatic('mimeType', Array)
#5 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(781): Illuminate\\Filesystem\\Filesystem::{closure}()
#6 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\b341978a0c2696830a3f4a02337355ec.php(7): rescue(Object(Closure), 'image/x-icon')
#7 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#8 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#9 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#10 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#11 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#12 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#13 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#14 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(843): Illuminate\\View\\View->render()
#15 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->header()
#16 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\06b7c83bcd3880a29c8bd3ab9cee515f.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('header', Array)
#17 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#18 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#19 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#20 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#21 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#22 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#23 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#24 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#25 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header-meta', 'theme.muhrak::p...', Array)
#26 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header-meta')
#27 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\a5eb6bda54d32602bade0b9f4bde5ca8.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#28 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#29 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#30 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#31 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#32 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#33 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#34 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#35 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(499): Illuminate\\View\\View->render()
#36 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(485): Botble\\Theme\\Theme->loadPartial('header', 'theme.muhrak::p...', Array)
#37 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->partial('header')
#38 D:\\laragon\\www\\muhrak\\storage\\framework\\views\\eeb44d511fa0e5044fd5eb04e92ce32b.php(1): Illuminate\\Support\\Facades\\Facade::__callStatic('partial', Array)
#39 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('D:\\\\laragon\\\\www\\\\...')
#40 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#41 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('D:\\\\laragon\\\\www\\\\...', Array)
#42 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(75): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('D:\\\\laragon\\\\www\\\\...', Array)
#43 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(209): Illuminate\\View\\Engines\\CompilerEngine->get('D:\\\\laragon\\\\www\\\\...', Array)
#44 D:\\laragon\\www\\muhrak\\platform\\packages\\shortcode\\src\\View\\View.php(50): Illuminate\\View\\View->getContents()
#45 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(161): Botble\\Shortcode\\View\\View->renderContents()
#46 D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\Theme.php(794): Illuminate\\View\\View->render()
#47 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Controllers\\Customers\\LoginController.php(45): Botble\\Theme\\Theme->render()
#48 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): Botble\\Ecommerce\\Http\\Controllers\\Customers\\LoginController->showLoginForm()
#49 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('showLoginForm', Array)
#50 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(Botble\\Ecommerce\\Http\\Controllers\\Customers\\LoginController), 'showLoginForm')
#51 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#52 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#53 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#54 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\RedirectIfCustomer.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\RedirectIfCustomer->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocalizationRedirectFilter.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocalizationRedirectFilter->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Http\\Middleware\\LocaleSessionRedirect.php(81): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Language\\Http\\Middleware\\LocaleSessionRedirect->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Botble\\Base\\Http\\Middleware\\CoreMiddleware->Botble\\Base\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#62 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\CoreMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\CoreMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureCouponMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureCouponMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Http\\Middleware\\CaptureFootprintsMiddleware.php(23): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Ecommerce\\Http\\Middleware\\CaptureFootprintsMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\HttpsProtocolMiddleware.php(16): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\HttpsProtocolMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\AdminLocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\LocaleMiddleware.php(29): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Base\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#75 D:\\laragon\\www\\muhrak\\platform\\packages\\installer\\src\\Http\\Middleware\\RedirectIfNotInstalledMiddleware.php(17): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#76 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\Installer\\Http\\Middleware\\RedirectIfNotInstalledMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#77 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#78 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#79 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#80 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#81 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#82 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#83 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#84 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#85 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#86 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#87 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#88 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#89 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#90 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#91 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#92 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#93 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#94 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#95 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#96 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#97 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#98 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#99 D:\\laragon\\www\\muhrak\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#100 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#101 D:\\laragon\\www\\muhrak\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#102 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#103 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#104 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#105 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#106 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#107 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#108 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#109 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#110 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#111 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#112 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#113 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#114 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#115 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#116 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#117 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#118 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#119 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#120 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#121 D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#122 D:\\laragon\\www\\muhrak\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#123 {main}
"} 
