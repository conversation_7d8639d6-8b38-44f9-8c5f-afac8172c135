@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="ps-section--account-setting">
        <div class="ps-section__header">
            <h3>{{ __('Agent Analytics') }} - {{ $agent->name }}</h3>
            <p class="text-muted">{{ __('View your agent performance and customer interactions') }}</p>
        </div>
        <div class="ps-section__content">
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-primary">{{ count($conversations) }}</h5>
                            <p class="card-text">{{ __('Total Conversations') }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-success">{{ count($leads) }}</h5>
                            <p class="card-text">{{ __('Leads Generated') }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-info">{{ number_format($agent->getTotalCharacterCount()) }}</h5>
                            <p class="card-text">{{ __('Training Characters') }}</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <h5 class="card-title text-warning">
                                @if($agent->last_trained_at)
                                    {{ $agent->last_trained_at->diffForHumans() }}
                                @else
                                    {{ __('Never') }}
                                @endif
                            </h5>
                            <p class="card-text">{{ __('Last Trained') }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-8">
                    <!-- Recent Conversations -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">{{ __('Recent Conversations') }}</h5>
                            @if($agent->chatbot_id)
                                <a href="https://www.chatbase.co/chatbot/{{ $agent->chatbot_id }}/analytics" 
                                   target="_blank" class="btn btn-sm btn-outline-primary">
                                    <i class="ti ti-external-link"></i>
                                    {{ __('View Full Analytics') }}
                                </a>
                            @endif
                        </div>
                        <div class="card-body">
                            @if(count($conversations) > 0)
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Date') }}</th>
                                                <th>{{ __('Messages') }}</th>
                                                <th>{{ __('Duration') }}</th>
                                                <th>{{ __('Status') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach(array_slice($conversations, 0, 10) as $conversation)
                                                <tr>
                                                    <td>
                                                        @if(isset($conversation['createdAt']))
                                                            {{ \Carbon\Carbon::parse($conversation['createdAt'])->format('M j, Y H:i') }}
                                                        @else
                                                            {{ __('Unknown') }}
                                                        @endif
                                                    </td>
                                                    <td>{{ $conversation['messageCount'] ?? 0 }}</td>
                                                    <td>
                                                        @if(isset($conversation['duration']))
                                                            {{ gmdate('H:i:s', $conversation['duration']) }}
                                                        @else
                                                            {{ __('N/A') }}
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-{{ ($conversation['status'] ?? 'active') === 'completed' ? 'success' : 'primary' }}">
                                                            {{ ucfirst($conversation['status'] ?? 'active') }}
                                                        </span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="ti ti-message-circle" style="font-size: 3rem; color: #ccc;"></i>
                                    <p class="text-muted mt-2">{{ __('No conversations yet') }}</p>
                                    <p class="text-muted">{{ __('Conversations will appear here once customers start chatting with your agent') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Leads -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('Recent Leads') }}</h5>
                        </div>
                        <div class="card-body">
                            @if(count($leads) > 0)
                                @foreach(array_slice($leads, 0, 5) as $lead)
                                    <div class="border-bottom pb-2 mb-2">
                                        <div class="d-flex justify-content-between">
                                            <strong>{{ $lead['name'] ?? __('Anonymous') }}</strong>
                                            <small class="text-muted">
                                                @if(isset($lead['createdAt']))
                                                    {{ \Carbon\Carbon::parse($lead['createdAt'])->diffForHumans() }}
                                                @endif
                                            </small>
                                        </div>
                                        @if(isset($lead['email']))
                                            <div class="text-muted">{{ $lead['email'] }}</div>
                                        @endif
                                        @if(isset($lead['phone']))
                                            <div class="text-muted">{{ $lead['phone'] }}</div>
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                <div class="text-center py-3">
                                    <i class="ti ti-users" style="font-size: 2rem; color: #ccc;"></i>
                                    <p class="text-muted mt-2">{{ __('No leads yet') }}</p>
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ __('Quick Actions') }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <a href="{{ route('marketplace.vendor.chatbase.edit', $agent->id) }}" class="btn btn-outline-primary btn-sm">
                                    <i class="ti ti-edit"></i>
                                    {{ __('Edit Agent') }}
                                </a>
                                <a href="{{ route('marketplace.vendor.chatbase.training', $agent->id) }}" class="btn btn-outline-info btn-sm">
                                    <i class="ti ti-brain"></i>
                                    {{ __('Manage Training') }}
                                </a>
                                @if($agent->chatbot_id)
                                    <a href="https://www.chatbase.co/chatbot/{{ $agent->chatbot_id }}" 
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="ti ti-external-link"></i>
                                        {{ __('Chatbase Dashboard') }}
                                    </a>
                                @endif
                                <a href="{{ route('marketplace.vendor.chatbase.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="ti ti-arrow-left"></i>
                                    {{ __('Back to Agent') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
// Auto-refresh analytics every 5 minutes
setInterval(function() {
    if (document.visibilityState === 'visible') {
        location.reload();
    }
}, 300000); // 5 minutes
</script>
@endpush
