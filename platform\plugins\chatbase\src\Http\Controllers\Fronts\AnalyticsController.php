<?php

namespace Botble\Chatbase\Http\Controllers\Fronts;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Chatbase\Models\ChatbaseAgent;
use Botble\Chatbase\Services\ChatbaseApiService;
use Botble\Marketplace\Facades\MarketplaceHelper;
use Illuminate\Http\Request;

class AnalyticsController extends BaseController
{
    public function index(string $id, Request $request, ChatbaseApiService $apiService)
    {
        $this->pageTitle(__('Agent Analytics'));

        $user = auth('customer')->user();
        $store = $user->store;

        if (!$store) {
            abort(404);
        }

        $agent = ChatbaseAgent::where('id', $id)
            ->where('store_id', $store->id)
            ->firstOrFail();

        if (!$agent->isActive()) {
            return redirect()
                ->route('marketplace.vendor.chatbase.index')
                ->with('error_msg', __('Agent must be active to view analytics'));
        }

        // Get conversations from Chatbase API
        $conversations = $this->getConversations($apiService, $agent->chatbot_id);
        $leads = $this->getLeads($apiService, $agent->chatbot_id);

        $data = compact('user', 'store', 'agent', 'conversations', 'leads');

        if ($request->ajax()) {
            return $this
                ->httpResponse()
                ->setData([
                    'html' => MarketplaceHelper::view('vendor-dashboard.chatbase.analytics', $data)->render(),
                ]);
        }

        return MarketplaceHelper::view('vendor-dashboard.chatbase.analytics', $data);
    }

    protected function getConversations(ChatbaseApiService $apiService, string $chatbotId): array
    {
        try {
            $response = $apiService->getConversations($chatbotId);
            return $response['success'] ? $response['data'] : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    protected function getLeads(ChatbaseApiService $apiService, string $chatbotId): array
    {
        try {
            $response = $apiService->getLeads($chatbotId);
            return $response['success'] ? $response['data'] : [];
        } catch (\Exception $e) {
            return [];
        }
    }
}
