<?php

namespace Botble\Chatbase\Services;

use Bo<PERSON><PERSON>\Chatbase\Jobs\CreateChatbaseAgentJob;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Chatbase\Models\ChatbaseTrainingSource;
use Botble\Marketplace\Models\Store;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChatbaseAgentService
{
    protected ChatbaseApiService $apiService;

    public function __construct(ChatbaseApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    public function createAgentForStore(Store $store, array $data, bool $async = false): array
    {
        try {
            DB::beginTransaction();

            // Check if agent already exists for this store
            $existingAgent = ChatbaseAgent::where('store_id', $store->id)->first();
            if ($existingAgent) {
                return [
                    'success' => false,
                    'error' => 'Agent already exists for this store',
                ];
            }

            // Create local agent record
            $agent = ChatbaseAgent::create([
                'name' => $data['name'] ?? $store->name . ' Assistant',
                'description' => $data['description'] ?? 'AI assistant for ' . $store->name,
                'store_id' => $store->id,
                'customer_id' => $store->customer_id,
                'status' => 'creating',
                'settings' => $data['settings'] ?? [],
            ]);

            // Prepare training data
            $trainingText = $this->prepareTrainingData($store, $data);

            if ($async) {
                // Create agent asynchronously using job
                CreateChatbaseAgentJob::dispatch($agent, $trainingText);

                // Agent status remains 'creating' until job completes
                $chatbotId = null;
            } else {
                // Create chatbot via API synchronously
                $apiResponse = $this->apiService->createChatbot(
                    $agent->name,
                    $trainingText
                );

                if (!$apiResponse['success']) {
                    $agent->update([
                        'status' => 'error',
                        'error_message' => $apiResponse['error'],
                    ]);

                    DB::rollBack();
                    return $apiResponse;
                }

                // Update agent with chatbot ID
                $agent->update([
                    'chatbot_id' => $apiResponse['data']['chatbotId'],
                    'status' => 'active',
                    'last_trained_at' => now(),
                    'last_synced_at' => now(),
                ]);

                $chatbotId = $apiResponse['data']['chatbotId'];
            }

            // Create training sources if provided
            if (!empty($data['training_sources'])) {
                $this->createTrainingSources($agent, $data['training_sources']);
            }

            DB::commit();

            return [
                'success' => true,
                'agent' => $agent,
                'chatbot_id' => $chatbotId,
                'async' => $async,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating Chatbase agent: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to create agent: ' . $e->getMessage(),
            ];
        }
    }

    public function updateAgent(ChatbaseAgent $agent, array $data): array
    {
        try {
            DB::beginTransaction();

            // Update local agent
            $agent->update([
                'name' => $data['name'] ?? $agent->name,
                'description' => $data['description'] ?? $agent->description,
                'settings' => array_merge($agent->settings ?? [], $data['settings'] ?? []),
            ]);

            // Update via API if chatbot exists
            if ($agent->chatbot_id) {
                $apiData = [];

                if (isset($data['name'])) {
                    $apiData['chatbotName'] = $data['name'];
                }

                if (!empty($apiData)) {
                    $apiResponse = $this->apiService->updateChatbot($agent->chatbot_id, $apiData);

                    if (!$apiResponse['success']) {
                        DB::rollBack();
                        return $apiResponse;
                    }
                }

                $agent->update(['last_synced_at' => now()]);
            }

            DB::commit();

            return [
                'success' => true,
                'agent' => $agent->fresh(),
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating Chatbase agent: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to update agent: ' . $e->getMessage(),
            ];
        }
    }

    public function deleteAgent(ChatbaseAgent $agent): array
    {
        try {
            DB::beginTransaction();

            // Delete from Chatbase API if chatbot exists
            if ($agent->chatbot_id) {
                $apiResponse = $this->apiService->deleteChatbot($agent->chatbot_id);

                if (!$apiResponse['success']) {
                    Log::warning('Failed to delete chatbot from Chatbase API: ' . $apiResponse['error']);
                    // Continue with local deletion even if API deletion fails
                }
            }

            // Delete local records
            $agent->trainingSources()->delete();
            $agent->delete();

            DB::commit();

            return [
                'success' => true,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error deleting Chatbase agent: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => 'Failed to delete agent: ' . $e->getMessage(),
            ];
        }
    }

    protected function prepareTrainingData(Store $store, array $data): string
    {
        $trainingText = [];

        // Store basic information
        $trainingText[] = "Store Name: " . $store->name;
        $trainingText[] = "Store Description: " . ($store->description ?: 'No description available');

        if ($store->address) {
            $trainingText[] = "Store Address: " . $store->address;
        }

        if ($store->phone) {
            $trainingText[] = "Store Phone: " . $store->phone;
        }

        if ($store->email) {
            $trainingText[] = "Store Email: " . $store->email;
        }

        // Add custom training text if provided
        if (!empty($data['training_text'])) {
            $trainingText[] = "Additional Information: " . $data['training_text'];
        }

        return implode("\n\n", $trainingText);
    }

    protected function createTrainingSources(ChatbaseAgent $agent, array $sources): void
    {
        foreach ($sources as $source) {
            $trainingSource = ChatbaseTrainingSource::create([
                'agent_id' => $agent->id,
                'type' => $source['type'],
                'title' => $source['title'],
                'content' => $source['content'] ?? null,
                'url' => $source['url'] ?? null,
                'file_path' => $source['file_path'] ?? null,
                'status' => 'active',
            ]);

            $trainingSource->updateCharacterCount();
        }
    }
}
