@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="ps-section--account-setting">
        <div class="ps-section__header">
            <h3>{{ __('plugins/chatbase::chatbase.dashboard.title') }}</h3>
        </div>
        <div class="ps-section__content">
            @if(!$agent)
                <div class="alert alert-info">
                    <h4 class="alert-heading">{{ __('plugins/chatbase::chatbase.dashboard.no_agent') }}</h4>
                    <p>{{ __('plugins/chatbase::chatbase.dashboard.no_agent_description') }}</p>
                    <hr>
                    <a href="{{ route('marketplace.vendor.chatbase.create') }}" class="btn btn-primary">
                        <i class="ti ti-plus"></i>
                        {{ __('plugins/chatbase::chatbase.dashboard.create_agent') }}
                    </a>
                </div>
            @else
                <div class="row">
                    <div class="col-md-8">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ __('plugins/chatbase::chatbase.dashboard.agent_info') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>{{ __('Name') }}:</strong>
                                        <p>{{ $agent->name }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>{{ __('plugins/chatbase::chatbase.dashboard.agent_status') }}:</strong>
                                        <p>
                                            @if($agent->status === 'active')
                                                <span class="badge bg-success">{{ __('plugins/chatbase::chatbase.agent.status.active') }}</span>
                                            @elseif($agent->status === 'creating')
                                                <span class="badge bg-warning">{{ __('plugins/chatbase::chatbase.agent.status.creating') }}</span>
                                            @elseif($agent->status === 'error')
                                                <span class="badge bg-danger">{{ __('plugins/chatbase::chatbase.agent.status.error') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('plugins/chatbase::chatbase.agent.status.draft') }}</span>
                                            @endif
                                        </p>
                                    </div>
                                </div>

                                @if($agent->description)
                                    <div class="row">
                                        <div class="col-12">
                                            <strong>{{ __('Description') }}:</strong>
                                            <p>{{ $agent->description }}</p>
                                        </div>
                                    </div>
                                @endif

                                @if($agent->chatbot_id)
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <strong>{{ __('plugins/chatbase::chatbase.dashboard.chatbot_id') }}:</strong>
                                            <p><code>{{ $agent->chatbot_id }}</code></p>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>{{ __('plugins/chatbase::chatbase.dashboard.widget_status') }}:</strong>
                                            <p>
                                                @if($agent->getSettingValue('widget.enabled', true))
                                                    <span class="badge bg-success">{{ __('plugins/chatbase::chatbase.dashboard.widget_enabled') }}</span>
                                                @else
                                                    <span class="badge bg-secondary">{{ __('plugins/chatbase::chatbase.dashboard.widget_disabled') }}</span>
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                @endif

                                @if($agent->hasError() && $agent->error_message)
                                    <div class="row">
                                        <div class="col-12">
                                            <strong>{{ __('plugins/chatbase::chatbase.dashboard.error_message') }}:</strong>
                                            <div class="alert alert-danger">{{ $agent->error_message }}</div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ __('plugins/chatbase::chatbase.dashboard.training_sources') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>{{ __('plugins/chatbase::chatbase.dashboard.total_characters') }}:</strong>
                                        <p>{{ number_format($agent->getTotalCharacterCount()) }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>{{ __('plugins/chatbase::chatbase.dashboard.last_trained') }}:</strong>
                                        <p>{{ $agent->last_trained_at ? $agent->last_trained_at->diffForHumans() : __('plugins/chatbase::chatbase.dashboard.never') }}</p>
                                    </div>
                                </div>
                                
                                @if($agent->trainingSources->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Type') }}</th>
                                                    <th>{{ __('Title') }}</th>
                                                    <th>{{ __('Content') }}</th>
                                                    <th>{{ __('Characters') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($agent->trainingSources->take(5) as $source)
                                                    <tr>
                                                        <td>
                                                            <span class="badge bg-info">{{ ucfirst($source->type) }}</span>
                                                        </td>
                                                        <td>{{ $source->title }}</td>
                                                        <td>{{ Str::limit($source->getDisplayContent(), 50) }}</td>
                                                        <td>{{ number_format($source->character_count) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted">{{ __('plugins/chatbase::chatbase.training.no_sources') }}</p>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">{{ __('plugins/chatbase::chatbase.dashboard.manage_agent') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <a href="{{ route('marketplace.vendor.chatbase.edit', $agent->id) }}" class="btn btn-primary">
                                        <i class="ti ti-edit"></i>
                                        {{ __('plugins/chatbase::chatbase.dashboard.edit_agent') }}
                                    </a>
                                    
                                    <a href="{{ route('marketplace.vendor.chatbase.training', $agent->id) }}" class="btn btn-info">
                                        <i class="ti ti-brain"></i>
                                        {{ __('plugins/chatbase::chatbase.dashboard.train_agent') }}
                                    </a>
                                    
                                    @if($agent->chatbot_id)
                                        <a href="https://www.chatbase.co/chatbot/{{ $agent->chatbot_id }}/analytics" target="_blank" class="btn btn-success">
                                            <i class="ti ti-chart-line"></i>
                                            {{ __('plugins/chatbase::chatbase.dashboard.view_analytics') }}
                                        </a>
                                    @endif
                                    
                                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAgentModal">
                                        <i class="ti ti-trash"></i>
                                        {{ __('plugins/chatbase::chatbase.dashboard.delete_agent') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Confirmation Modal -->
                <div class="modal fade" id="deleteAgentModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">{{ __('Confirm Deletion') }}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>{{ __('Are you sure you want to delete this agent? This action cannot be undone.') }}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                                <form method="POST" action="{{ route('marketplace.vendor.chatbase.destroy', $agent->id) }}" style="display: inline;">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-danger">{{ __('Delete') }}</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
@endsection
