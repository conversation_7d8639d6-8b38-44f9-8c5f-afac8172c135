<?php

namespace Botble\Chatbase\Providers;

use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Facades\PanelSectionManager;
use Bo<PERSON>ble\Base\PanelSections\PanelSectionItem;
use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Chatbase\Models\ChatbaseAgent;
use Bo<PERSON>ble\Setting\PanelSections\SettingOthersPanelSection;
use Illuminate\Routing\Events\RouteMatched;

class ChatbaseServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        config([
            'packages.chatbase.general' => array_merge(
                config('packages.chatbase.general', []),
                config('plugins.chatbase.general', [])
            ),
        ]);

        $this->app->singleton(\Botble\Chatbase\Services\ChatbaseApiService::class);
        $this->app->singleton(\Botble\Chatbase\Services\ChatbaseAgentService::class);
        $this->app->singleton(\Botble\Chatbase\Supports\ChatbaseHelper::class);
    }

    public function boot(): void
    {
        $routeFiles = ['web', 'api'];

        // Only load vendor routes if marketplace plugin is active
        if (is_plugin_active('marketplace')) {
            $routeFiles[] = 'vendor';
        }

        $this
            ->setNamespace('plugins/chatbase')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['general'])
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes($routeFiles)
            ->loadMigrations()
            ->publishAssets();

        $this->app['events']->listen(RouteMatched::class, function (): void {
            $this->registerAdminMenus();
            $this->registerVendorMenus();
        });

        $this->registerHooks();
        $this->registerCommands();
        $this->registerPanelSections();
    }

    protected function registerAdminMenus(): void
    {
        // Admin menu is now handled by panel sections
        // Settings will appear in Settings > Other section
    }

    protected function registerVendorMenus(): void
    {
        if (!is_plugin_active('marketplace')) {
            return;
        }

        DashboardMenu::for('vendor')->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'marketplace.vendor.chatbase',
                    'priority' => 8,
                    'name' => __('Chatbase Agent'),
                    'url' => fn () => route('marketplace.vendor.chatbase.index'),
                    'icon' => 'ti ti-message-chatbot',
                ]);
        });
    }

    protected function registerHooks(): void
    {
        // Hook to add Chatbase widget to store pages
        add_filter('marketplace_store_single_page_footer', [$this, 'addChatbaseWidget'], 10, 1);

        // Hook to add widget to theme footer if auto-embed is enabled
        if (setting('chatbase_auto_embed_widget', true)) {
            add_action('theme.footer', [$this, 'addChatbaseWidgetToTheme']);
        }
    }

    public function addChatbaseWidget($store): string
    {
        if (!$store || !$store->customer) {
            return '';
        }

        $agent = ChatbaseAgent::where('store_id', $store->id)
            ->where('status', 'active')
            ->first();

        if (!$agent || !$agent->chatbot_id) {
            return '';
        }

        return view('plugins/chatbase::widget', compact('agent'))->render();
    }

    public function addChatbaseWidgetToTheme(): void
    {
        // Check if we're on a store page
        if (!request()->route() || !str_contains(request()->route()->getName() ?? '', 'public.store')) {
            return;
        }

        // Get store from current route
        $slug = request()->route('slug');
        if (!$slug) {
            return;
        }

        $store = \Botble\Marketplace\Models\Store::whereHas('slugable', function ($query) use ($slug) {
            $query->where('key', $slug);
        })->first();

        if ($store) {
            echo $this->addChatbaseWidget($store);
        }
    }

    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Botble\Chatbase\Commands\SyncChatbaseAgentsCommand::class,
            ]);
        }
    }

    protected function registerPanelSections(): void
    {
        PanelSectionManager::default()->beforeRendering(function (): void {
            PanelSectionManager::registerItem(
                SettingOthersPanelSection::class,
                fn () => PanelSectionItem::make('chatbase')
                    ->setTitle(trans('plugins/chatbase::chatbase.settings.title'))
                    ->withIcon('ti ti-message-chatbot')
                    ->withDescription(trans('plugins/chatbase::chatbase.settings.description'))
                    ->withPriority(150)
                    ->withRoute('chatbase.settings')
                    ->withPermissions(['chatbase.settings'])
            );
        });
    }
}
