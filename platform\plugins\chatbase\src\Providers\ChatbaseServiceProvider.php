<?php

namespace Bo<PERSON>ble\Chatbase\Providers;

use Botble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Chatbase\Models\ChatbaseAgent;

use Illuminate\Routing\Events\RouteMatched;

class ChatbaseServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        config([
            'packages.chatbase.general' => array_merge(
                config('packages.chatbase.general', []),
                config('plugins.chatbase.general', [])
            ),
        ]);

        $this->app->singleton(\Botble\Chatbase\Services\ChatbaseApiService::class);
        $this->app->singleton(\Botble\Chatbase\Services\ChatbaseAgentService::class);
        $this->app->singleton(\Botble\Chatbase\Supports\ChatbaseHelper::class);
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/chatbase')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['general'])
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes()
            ->loadMigrations()
            ->publishAssets();

        $this->app['events']->listen(RouteMatched::class, function (): void {
            $this->registerAdminMenus();
            $this->registerVendorMenus();
        });

        $this->registerHooks();
        $this->registerCommands();
    }

    protected function registerAdminMenus(): void
    {
        DashboardMenu::beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'cms-plugins-chatbase',
                    'priority' => 120,
                    'parent_id' => 'cms-core-settings',
                    'name' => 'plugins/chatbase::chatbase.name',
                    'icon' => 'ti ti-message-chatbot',
                    'url' => route('chatbase.settings'),
                    'permissions' => ['chatbase.settings'],
                ]);
        });
    }

    protected function registerVendorMenus(): void
    {
        DashboardMenu::for('vendor')->beforeRetrieving(function (): void {
            DashboardMenu::make()
                ->registerItem([
                    'id' => 'marketplace.vendor.chatbase',
                    'priority' => 8,
                    'name' => __('Chatbase Agent'),
                    'url' => fn () => route('marketplace.vendor.chatbase.index'),
                    'icon' => 'ti ti-message-chatbot',
                ]);
        });
    }

    protected function registerHooks(): void
    {
        // Hook to add Chatbase widget to store pages
        add_filter('marketplace_store_single_page_footer', [$this, 'addChatbaseWidget'], 10, 1);

        // Hook to add widget to theme footer if auto-embed is enabled
        if (setting('chatbase_auto_embed_widget', true)) {
            add_action('theme.footer', [$this, 'addChatbaseWidgetToTheme']);
        }
    }

    public function addChatbaseWidget($store): string
    {
        if (!$store || !$store->customer) {
            return '';
        }

        $agent = ChatbaseAgent::where('store_id', $store->id)
            ->where('status', 'active')
            ->first();

        if (!$agent || !$agent->chatbot_id) {
            return '';
        }

        return view('plugins/chatbase::widget', compact('agent'))->render();
    }

    public function addChatbaseWidgetToTheme(): void
    {
        // Check if we're on a store page
        if (!request()->route() || !str_contains(request()->route()->getName() ?? '', 'public.store')) {
            return;
        }

        // Get store from current route
        $slug = request()->route('slug');
        if (!$slug) {
            return;
        }

        $store = \Botble\Marketplace\Models\Store::whereHas('slugable', function ($query) use ($slug) {
            $query->where('key', $slug);
        })->first();

        if ($store) {
            echo $this->addChatbaseWidget($store);
        }
    }

    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Botble\Chatbase\Commands\SyncChatbaseAgentsCommand::class,
            ]);
        }
    }
}
