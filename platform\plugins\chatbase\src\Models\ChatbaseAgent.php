<?php

namespace Botble\Chatbase\Models;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Botble\Ecommerce\Models\Customer;
use Botble\Marketplace\Models\Store;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ChatbaseAgent extends BaseModel
{
    protected $table = 'chatbase_agents';

    protected $fillable = [
        'name',
        'description',
        'store_id',
        'customer_id',
        'chatbot_id',
        'status',
        'settings',
        'training_data',
        'error_message',
        'last_trained_at',
        'last_synced_at',
    ];

    protected $casts = [
        'settings' => 'array',
        'training_data' => 'array',
        'last_trained_at' => 'datetime',
        'last_synced_at' => 'datetime',
    ];

    public function store(): BelongsTo
    {
        return $this->belongsTo(Store::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function trainingSources(): HasMany
    {
        return $this->hasMany(ChatbaseTrainingSource::class, 'agent_id');
    }

    public function isActive(): bool
    {
        return $this->status === 'active' && !empty($this->chatbot_id);
    }

    public function isCreating(): bool
    {
        return $this->status === 'creating';
    }

    public function hasError(): bool
    {
        return $this->status === 'error';
    }

    public function getSettingValue(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    public function updateSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['settings' => $settings]);
    }

    public function getTotalCharacterCount(): int
    {
        return $this->trainingSources()->sum('character_count');
    }

    public function getWidgetCode(): string
    {
        if (!$this->isActive()) {
            return '';
        }

        $settings = $this->settings ?? [];
        $position = data_get($settings, 'widget.position', 'bottom-right');
        $theme = data_get($settings, 'widget.theme', 'light');

        return sprintf(
            '<script>
                window.embeddedChatbotConfig = {
                    chatbotId: "%s",
                    domain: "www.chatbase.co"
                }
            </script>
            <script src="https://www.chatbase.co/embed.min.js" chatbotId="%s" domain="www.chatbase.co" defer></script>',
            $this->chatbot_id,
            $this->chatbot_id
        );
    }
}
