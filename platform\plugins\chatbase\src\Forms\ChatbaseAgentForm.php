<?php

namespace Bo<PERSON>ble\Chatbase\Forms;

use Bo<PERSON>ble\Base\Forms\FieldOptions\OnOffFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Bo<PERSON>ble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\Fields\OnOffCheckboxField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\FormAbstract;
use Botble\Chatbase\Http\Requests\ChatbaseAgentRequest;
use Botble\Chatbase\Models\ChatbaseAgent;

class ChatbaseAgentForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->model(ChatbaseAgent::class)
            ->setValidatorClass(ChatbaseAgentRequest::class)
            ->add(
                'name',
                TextField::class,
                TextFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.name'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.name_helper'))
                    ->required()
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.name_placeholder'),
                        'data-counter' => 255,
                    ])
            )
            ->add(
                'description',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.description'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.description_helper'))
                    ->rows(3)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.description_placeholder'),
                    ])
            )
            ->add(
                'training_text',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.training_text'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.training_text_helper'))
                    ->rows(6)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.training_text_placeholder'),
                    ])
            )
            ->add(
                'model',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.model'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.model_helper'))
                    ->choices([
                        'gpt-4o-mini' => 'GPT-4o Mini (Recommended)',
                        'gpt-4o' => 'GPT-4o',
                        'gpt-4-turbo' => 'GPT-4 Turbo',
                        'gpt-4' => 'GPT-4',
                        'claude-3-5-sonnet' => 'Claude 3.5 Sonnet',
                        'claude-3-opus' => 'Claude 3 Opus',
                        'claude-3-haiku' => 'Claude 3 Haiku',
                        'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                        'gemini-1.5-flash' => 'Gemini 1.5 Flash',
                    ])
                    ->defaultValue(setting('chatbase_default_model', 'gpt-4o-mini'))
            )
            ->add(
                'temperature',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.temperature'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.temperature_helper'))
                    ->choices([
                        '0' => '0 - ' . trans('plugins/chatbase::chatbase.agent.temperature_focused'),
                        '0.3' => '0.3 - ' . trans('plugins/chatbase::chatbase.agent.temperature_balanced'),
                        '0.7' => '0.7 - ' . trans('plugins/chatbase::chatbase.agent.temperature_creative'),
                        '1' => '1 - ' . trans('plugins/chatbase::chatbase.agent.temperature_very_creative'),
                    ])
                    ->defaultValue('0')
            )
            ->add(
                'widget_enabled',
                OnOffCheckboxField::class,
                OnOffFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.widget_enabled'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.widget_enabled_helper'))
                    ->defaultValue(true)
            )
            ->add(
                'widget_position',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.widget_position'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.widget_position_helper'))
                    ->choices([
                        'bottom-right' => trans('plugins/chatbase::chatbase.settings.position_bottom_right'),
                        'bottom-left' => trans('plugins/chatbase::chatbase.settings.position_bottom_left'),
                        'top-right' => trans('plugins/chatbase::chatbase.settings.position_top_right'),
                        'top-left' => trans('plugins/chatbase::chatbase.settings.position_top_left'),
                    ])
                    ->defaultValue(setting('chatbase_widget_position', 'bottom-right'))
            )
            ->add(
                'widget_theme',
                SelectField::class,
                SelectFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.widget_theme'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.widget_theme_helper'))
                    ->choices([
                        'light' => trans('plugins/chatbase::chatbase.settings.theme_light'),
                        'dark' => trans('plugins/chatbase::chatbase.settings.theme_dark'),
                        'auto' => trans('plugins/chatbase::chatbase.settings.theme_auto'),
                    ])
                    ->defaultValue(setting('chatbase_widget_theme', 'light'))
            )
            ->add(
                'custom_instructions',
                TextareaField::class,
                TextareaFieldOption::make()
                    ->label(trans('plugins/chatbase::chatbase.agent.custom_instructions'))
                    ->helperText(trans('plugins/chatbase::chatbase.agent.custom_instructions_helper'))
                    ->rows(4)
                    ->attributes([
                        'placeholder' => trans('plugins/chatbase::chatbase.agent.custom_instructions_placeholder'),
                    ])
            );
    }
}
