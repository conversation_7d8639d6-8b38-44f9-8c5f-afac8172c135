<?php

use Bo<PERSON><PERSON>\Chatbase\Http\Controllers\Fronts\ChatbaseController;
use Bo<PERSON>ble\Chatbase\Http\Controllers\Fronts\TrainingSourceController;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace' => 'Botble\Chatbase\Http\Controllers\Fronts',
    'prefix' => config('plugins.marketplace.general.vendor_panel_dir', 'vendor'),
    'as' => 'marketplace.vendor.',
    'middleware' => ['web', 'core', 'vendor'],
], function (): void {
    Route::group(['prefix' => 'chatbase', 'as' => 'chatbase.'], function (): void {
        Route::get('/', [ChatbaseController::class, 'index'])->name('index');
        Route::get('create', [ChatbaseController::class, 'create'])->name('create');
        Route::post('/', [ChatbaseController::class, 'store'])->name('store');
        Route::get('{id}/edit', [ChatbaseController::class, 'edit'])->name('edit');
        Route::put('{id}', [ChatbaseController::class, 'update'])->name('update');
        Route::delete('{id}', [ChatbaseController::class, 'destroy'])->name('destroy');
        Route::get('{id}/training', [ChatbaseController::class, 'training'])->name('training');

        // Training source routes
        Route::post('{agentId}/training-sources', [TrainingSourceController::class, 'store'])->name('training-sources.store');
        Route::put('{agentId}/training-sources/{sourceId}', [TrainingSourceController::class, 'update'])->name('training-sources.update');
        Route::delete('{agentId}/training-sources/{sourceId}', [TrainingSourceController::class, 'destroy'])->name('training-sources.destroy');
    });
});
