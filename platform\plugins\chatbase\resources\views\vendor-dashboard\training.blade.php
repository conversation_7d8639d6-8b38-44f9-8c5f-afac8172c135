@extends(MarketplaceHelper::viewPath('vendor-dashboard.layouts.master'))

@section('content')
    <div class="ps-section--account-setting">
        <div class="ps-section__header">
            <h3>{{ __('plugins/chatbase::chatbase.training.title') }}</h3>
            <p class="text-muted">{{ __('plugins/chatbase::chatbase.training.description') }}</p>
        </div>
        <div class="ps-section__content">
            <div class="row">
                <div class="col-md-8">
                    <!-- Training Sources List -->
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">{{ __('plugins/chatbase::chatbase.dashboard.training_sources') }}</h5>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                                <i class="ti ti-plus"></i>
                                {{ __('plugins/chatbase::chatbase.training.add_source') }}
                            </button>
                        </div>
                        <div class="card-body">
                            @if($agent->trainingSources->count() > 0)
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>{{ __('Type') }}</th>
                                                <th>{{ __('Title') }}</th>
                                                <th>{{ __('Content') }}</th>
                                                <th>{{ __('Characters') }}</th>
                                                <th>{{ __('Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($agent->trainingSources as $source)
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-info">{{ ucfirst($source->type) }}</span>
                                                    </td>
                                                    <td>{{ $source->title }}</td>
                                                    <td>{{ Str::limit($source->getDisplayContent(), 100) }}</td>
                                                    <td>{{ number_format($source->character_count) }}</td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <button type="button" class="btn btn-outline-primary" 
                                                                    onclick="editSource({{ $source->id }}, '{{ $source->type }}', '{{ addslashes($source->title) }}', '{{ addslashes($source->content ?? $source->url) }}')">
                                                                <i class="ti ti-edit"></i>
                                                            </button>
                                                            <form method="POST" action="{{ route('marketplace.vendor.chatbase.training-sources.destroy', [$agent->id, $source->id]) }}" 
                                                                  style="display: inline;" onsubmit="return confirm('Are you sure?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="btn btn-outline-danger">
                                                                    <i class="ti ti-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <i class="ti ti-brain" style="font-size: 3rem; color: #ccc;"></i>
                                    <p class="text-muted mt-2">{{ __('plugins/chatbase::chatbase.training.no_sources') }}</p>
                                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSourceModal">
                                        {{ __('plugins/chatbase::chatbase.training.add_source') }}
                                    </button>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- Agent Info -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">{{ $agent->name }}</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <strong>{{ __('plugins/chatbase::chatbase.dashboard.total_characters') }}:</strong>
                                <p class="mb-0">{{ number_format($agent->getTotalCharacterCount()) }}</p>
                            </div>
                            <div class="mb-3">
                                <strong>{{ __('plugins/chatbase::chatbase.dashboard.last_trained') }}:</strong>
                                <p class="mb-0">{{ $agent->last_trained_at ? $agent->last_trained_at->diffForHumans() : __('plugins/chatbase::chatbase.dashboard.never') }}</p>
                            </div>
                            <div class="d-grid">
                                <a href="{{ route('marketplace.vendor.chatbase.index') }}" class="btn btn-secondary">
                                    <i class="ti ti-arrow-left"></i>
                                    {{ __('Back to Agent') }}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Source Modal -->
    <div class="modal fade" id="addSourceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <form id="sourceForm" method="POST" action="{{ route('marketplace.vendor.chatbase.training-sources.store', $agent->id) }}">
                    @csrf
                    <div class="modal-header">
                        <h5 class="modal-title">{{ __('plugins/chatbase::chatbase.training.add_source') }}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="source_type" class="form-label">{{ __('plugins/chatbase::chatbase.training.source_type') }}</label>
                            <select class="form-select" id="source_type" name="type" required onchange="toggleSourceFields()">
                                <option value="">{{ __('Select type') }}</option>
                                <option value="text">{{ __('plugins/chatbase::chatbase.training.types.text') }}</option>
                                <option value="url">{{ __('plugins/chatbase::chatbase.training.types.url') }}</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="source_title" class="form-label">{{ __('plugins/chatbase::chatbase.training.source_title') }}</label>
                            <input type="text" class="form-control" id="source_title" name="title" required>
                        </div>

                        <div class="mb-3" id="content_field" style="display: none;">
                            <label for="source_content" class="form-label">{{ __('plugins/chatbase::chatbase.training.source_content') }}</label>
                            <textarea class="form-control" id="source_content" name="content" rows="8" 
                                      placeholder="{{ __('Enter your training content here...') }}"></textarea>
                            <div class="form-text">
                                <span id="char_count">0</span> characters
                            </div>
                        </div>

                        <div class="mb-3" id="url_field" style="display: none;">
                            <label for="source_url" class="form-label">{{ __('plugins/chatbase::chatbase.training.source_url') }}</label>
                            <input type="url" class="form-control" id="source_url" name="url" 
                                   placeholder="https://example.com/page">
                            <div class="form-text">
                                {{ __('Enter a URL to scrape content from') }}
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
                        <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('footer')
<script>
function toggleSourceFields() {
    const type = document.getElementById('source_type').value;
    const contentField = document.getElementById('content_field');
    const urlField = document.getElementById('url_field');
    
    contentField.style.display = type === 'text' ? 'block' : 'none';
    urlField.style.display = type === 'url' ? 'block' : 'none';
    
    // Clear fields when switching types
    if (type !== 'text') {
        document.getElementById('source_content').value = '';
    }
    if (type !== 'url') {
        document.getElementById('source_url').value = '';
    }
}

function editSource(id, type, title, content) {
    const modal = document.getElementById('addSourceModal');
    const form = document.getElementById('sourceForm');
    const modalTitle = modal.querySelector('.modal-title');
    
    // Update modal for editing
    modalTitle.textContent = 'Edit Training Source';
    form.action = form.action.replace('/training-sources', `/training-sources/${id}`);
    form.insertAdjacentHTML('afterbegin', '<input type="hidden" name="_method" value="PUT">');
    
    // Fill form fields
    document.getElementById('source_type').value = type;
    document.getElementById('source_title').value = title;
    
    if (type === 'text') {
        document.getElementById('source_content').value = content;
    } else if (type === 'url') {
        document.getElementById('source_url').value = content;
    }
    
    toggleSourceFields();
    
    // Show modal
    new bootstrap.Modal(modal).show();
}

// Character counter
document.addEventListener('DOMContentLoaded', function() {
    const contentTextarea = document.getElementById('source_content');
    const charCount = document.getElementById('char_count');
    
    if (contentTextarea && charCount) {
        contentTextarea.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });
    }
    
    // Reset form when modal is hidden
    document.getElementById('addSourceModal').addEventListener('hidden.bs.modal', function() {
        const form = document.getElementById('sourceForm');
        const modalTitle = this.querySelector('.modal-title');
        
        form.reset();
        modalTitle.textContent = '{{ __('plugins/chatbase::chatbase.training.add_source') }}';
        form.action = '{{ route('marketplace.vendor.chatbase.training-sources.store', $agent->id) }}';
        
        // Remove method override if exists
        const methodInput = form.querySelector('input[name="_method"]');
        if (methodInput) {
            methodInput.remove();
        }
        
        toggleSourceFields();
    });
});
</script>
@endpush
